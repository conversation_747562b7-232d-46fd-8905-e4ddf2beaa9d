package com.example.onelinediary
import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.media.MediaPlayer
import android.media.MediaRecorder
import android.media.MediaScannerConnection
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.provider.MediaStore
import android.util.Log
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.Toast
import android.widget.VideoView
import android.content.res.Configuration
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.slideInVertically
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.*
import androidx.compose.ui.text.font.FontFamily

import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.Alignment
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.geometry.Offset
import androidx.compose.foundation.Canvas
import kotlin.math.cos
import kotlin.math.sin
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.core.view.WindowCompat
import androidx.compose.ui.graphics.toArgb
import androidx.documentfile.provider.DocumentFile
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream
import com.example.onelinediary.HistoryScreen
import com.example.onelinediary.HistoryDayScreen
import com.example.onelinediary.DownloadScreen
import com.example.onelinediary.ui.theme.OneLineDiaryTheme
import com.example.onelinediary.ui.theme.LocalAppTheme
import com.example.onelinediary.ui.theme.ThemeManager
import com.example.onelinediary.ui.theme.AppTheme
import com.example.onelinediary.components.standardOutlinedButtonColors
import com.example.onelinediary.components.getLighterTintColor
import com.example.onelinediary.components.getDarkerBackgroundColor
import com.example.onelinediary.components.getSoftPastelColor
import com.example.onelinediary.components.getSoftComplementaryBackgroundColor
import com.example.onelinediary.components.standardButtonElevation

// Helper function for custom icons
@Composable
fun CustomIcon(
    iconRes: Int,
    contentDescription: String?,
    modifier: Modifier = Modifier,
    tint: Color = LocalContentColor.current
) {
    Icon(
        painter = painterResource(id = iconRes),
        contentDescription = contentDescription,
        modifier = modifier,
        tint = tint
    )
}

// Flower design removed per user request

// All flower drawing functions removed per user request

class MainActivity : ComponentActivity() {
    private lateinit var saveDateManager: SaveDateManager
    var currentScreen = "start" // Changed from private to public
    var recordedAudioPath: String? = null
    var recordedVideoPath: String? = null // Store recorded video path
    var selectedHistoryDate: Calendar? = null // Store selected date for history view

    // Function to update system UI colors based on theme
    fun updateSystemUIColors(theme: AppTheme) {
        val backgroundColor = when (theme) {
            AppTheme.STANDARD -> Color(0xFFCCD5AE) // Green background (unchanged)
            AppTheme.BLACK_WHITE -> Color(0xFFF5F5F5) // White background (unchanged)
            AppTheme.BLUE -> Color(0xFFE8F4FD) // Very soft pastel blue background
            AppTheme.PINK -> Color(0xFFFDF0F5) // Very soft pastel pink background
            AppTheme.PURPLE -> Color(0xFFF5F0FD) // Very soft pastel purple background
            AppTheme.YELLOW -> Color(0xFFFFFAE8) // Very soft pastel yellow background
            AppTheme.ORANGE -> Color(0xFFFFF5E8) // Very soft pastel orange background
            AppTheme.RED -> Color(0xFFFDF0F0) // Very soft pastel red background
            AppTheme.TEAL -> Color(0xFFE8FDFD) // Very soft pastel teal background
            AppTheme.LAVENDER -> Color(0xFFF8F0FD) // Very soft pastel lavender background
        }

        // Update status bar and navigation bar colors
        window.statusBarColor = backgroundColor.toArgb()
        window.navigationBarColor = backgroundColor.toArgb()

        // Set light status bar for both themes since both use light backgrounds
        WindowCompat.getInsetsController(window, window.decorView).isAppearanceLightStatusBars = true
        WindowCompat.getInsetsController(window, window.decorView).isAppearanceLightNavigationBars = true
    }

    // Method to navigate between screens
    fun navigateToScreen(screenName: String) {
        Log.d("MainActivity", "Navigating to screen: $screenName (current: $currentScreen)")

        // Special handling for history navigation
        if (screenName == "history" && currentScreen == "history_day") {
            Log.d("MainActivity", "Special handling: Navigating from history_day to history")
            // Force clear any cached state that might be interfering with navigation
            selectedHistoryDate = null
        }

        // Update the current screen
        currentScreen = screenName

        // Trigger a recomposition of the App composable with the theme
        setContent {
            // Get the current theme from ThemeManager - use by to make it reactive
            val themeManager = ThemeManager.getInstance(this)
            val currentTheme by themeManager.currentTheme

            // Update system UI colors when theme changes
            LaunchedEffect(currentTheme) {
                updateSystemUIColors(currentTheme)
            }

            OneLineDiaryTheme(appTheme = currentTheme) {
                App(
                    saveText = { text -> saveToPhone(text, ContentType.TEXT) },
                    savePhoto = { text -> savePhoto(text) },
                    saveVideo = { text -> saveVideo(text) },
                    saveAudio = { text -> saveAudio(text) },
                    saveMood = { text -> saveMood(text) },
                    saveDateManager = saveDateManager
                )
            }
        }

        // Additional logging after navigation
        Log.d("MainActivity", "Navigation complete. Current screen is now: $currentScreen")
    }

    // Direct method to navigate to audio recorder
    fun navigateToAudioRecorder() {
        Log.d("MainActivity", "Direct navigation to audio recorder")
        // Update the current screen directly instead of using an intent
        // This avoids creating a new activity instance
        currentScreen = "audio_recorder"
        // Trigger a recomposition with the theme
        setContent {
            // Get the current theme from ThemeManager
            val themeManager = ThemeManager.getInstance(this)
            val currentTheme = themeManager.currentTheme.value

            OneLineDiaryTheme(appTheme = currentTheme) {
                App(
                    saveText = { text -> saveToPhone(text, ContentType.TEXT) },
                    savePhoto = { text -> savePhoto(text) },
                    saveVideo = { text -> saveVideo(text) },
                    saveAudio = { text -> saveAudio(text) },
                    saveMood = { text -> saveMood(text) },
                    saveDateManager = saveDateManager
                )
            }
        }
    }

    // Direct method to navigate to history screen
    fun navigateToHistory() {
        Log.d("MainActivity", "Direct navigation to history screen")
        // Update the current screen directly instead of using an intent
        // This avoids creating a new activity instance
        currentScreen = "history"
        // Clear any selected date
        selectedHistoryDate = null
        // Trigger a recomposition with the theme
        setContent {
            // Get the current theme from ThemeManager
            val themeManager = ThemeManager.getInstance(this)
            val currentTheme = themeManager.currentTheme.value

            OneLineDiaryTheme(appTheme = currentTheme) {
                App(
                    saveText = { text -> saveToPhone(text, ContentType.TEXT) },
                    savePhoto = { text -> savePhoto(text) },
                    saveVideo = { text -> saveVideo(text) },
                    saveAudio = { text -> saveAudio(text) },
                    saveMood = { text -> saveMood(text) },
                    saveDateManager = saveDateManager
                )
            }
        }

        // Log all saved dates for debugging
        Log.d("MainActivity", "All saved dates: ${saveDateManager.getAllSaveDates()}")
    }

    // Direct method to force navigation from history day to history screen
    fun forceNavigateToHistoryFromDay() {
        Log.d("MainActivity", "FORCE navigation from history day to history screen")
        // Clear any selected date
        selectedHistoryDate = null
        // Force the screen change
        currentScreen = "history"
        // Trigger a recomposition with the theme
        setContent {
            // Get the current theme from ThemeManager
            val themeManager = ThemeManager.getInstance(this)
            val currentTheme = themeManager.currentTheme.value

            OneLineDiaryTheme(appTheme = currentTheme) {
                App(
                    saveText = { text -> saveToPhone(text, ContentType.TEXT) },
                    savePhoto = { text -> savePhoto(text) },
                    saveVideo = { text -> saveVideo(text) },
                    saveAudio = { text -> saveAudio(text) },
                    saveMood = { text -> saveMood(text) },
                    saveDateManager = saveDateManager
                )
            }
        }
        Log.d("MainActivity", "FORCE navigation complete. Current screen is now: $currentScreen")
    }

    // Direct method to navigate to settings screen
    fun navigateToSettings() {
        Log.d("MainActivity", "Direct navigation to settings screen")
        // Update the current screen directly instead of using an intent
        // This avoids creating a new activity instance
        currentScreen = "settings"
        // Trigger a recomposition with the theme
        setContent {
            OneLineDiaryTheme(appTheme = ThemeManager.getInstance(this).currentTheme.value) {
                App(
                    saveText = { text -> saveToPhone(text, ContentType.TEXT) },
                    savePhoto = { text -> savePhoto(text) },
                    saveVideo = { text -> saveVideo(text) },
                    saveAudio = { text -> saveAudio(text) },
                    saveMood = { text -> saveMood(text) },
                    saveDateManager = saveDateManager
                )
            }
        }
    }

    // Direct method to navigate to download screen
    fun navigateToDownload() {
        Log.d("MainActivity", "Direct navigation to download screen")
        // Update the current screen directly instead of using an intent
        // This avoids creating a new activity instance
        currentScreen = "download"
        // Trigger a recomposition with the theme
        setContent {
            OneLineDiaryTheme(appTheme = ThemeManager.getInstance(this).currentTheme.value) {
                App(
                    saveText = { text -> saveToPhone(text, ContentType.TEXT) },
                    savePhoto = { text -> savePhoto(text) },
                    saveVideo = { text -> saveVideo(text) },
                    saveAudio = { text -> saveAudio(text) },
                    saveMood = { text -> saveMood(text) },
                    saveDateManager = saveDateManager
                )
            }
        }
    }

    // Direct method to navigate to new main menu screen
    fun navigateToNewMainMenu() {
        Log.d("MainActivity", "Direct navigation to new main menu screen")
        // Update the current screen directly instead of using an intent
        // This avoids creating a new activity instance
        currentScreen = "new_main_menu"
        // Trigger a recomposition with the theme to ensure proper background color
        setContent {
            OneLineDiaryTheme(appTheme = ThemeManager.getInstance(this).currentTheme.value) {
                App(
                    saveText = { text -> saveToPhone(text, ContentType.TEXT) },
                    savePhoto = { text -> savePhoto(text) },
                    saveVideo = { text -> saveVideo(text) },
                    saveAudio = { text -> saveAudio(text) },
                    saveMood = { text -> saveMood(text) },
                    saveDateManager = saveDateManager
                )
            }
        }
    }

    fun saveToPhone(text: String, contentType: ContentType = ContentType.TEXT) {
        val folderName = "OnLiDi"
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val dateStr = dateFormat.format(Date())

        // Use content type in the file name to avoid overwriting
        val fileName = "${dateStr}_${contentType.name.lowercase()}.txt"

        Log.d("MainActivity", "Saving $contentType content to file: $fileName")

        val folder = File(Environment.getExternalStoragePublicDirectory(
            Environment.DIRECTORY_DOCUMENTS), folderName)
        if (!folder.exists()) {
            folder.mkdirs()
        }

        val file = File(folder, fileName)
        file.writeText(text)

        // Record that this content type was saved today
        saveDateManager.recordSave(contentType)
    }

    fun savePhoto(text: String) {
        saveToPhone(text, ContentType.PHOTO)
    }

    fun saveVideo(text: String) {
        saveToPhone(text, ContentType.VIDEO)
    }

    fun saveAudio(text: String) {
        saveToPhone(text, ContentType.AUDIO)
    }

    fun saveMood(text: String) {
        // Save to file
        saveToPhone(text, ContentType.MOOD)

        // Also save to shared preferences for better retrieval
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val dateStr = dateFormat.format(Date())
        val diaryPrefs = getSharedPreferences("diary_prefs", Context.MODE_PRIVATE)

        // Save the mood text directly to shared preferences with the date key
        diaryPrefs.edit().putString("mood_$dateStr", text).apply()

        // Make sure this date is recorded in the saved dates set
        val allDates = saveDateManager.getAllSaveDates().toMutableSet()
        allDates.add(dateStr)
        val prefs = getSharedPreferences("one_line_diary_prefs", Context.MODE_PRIVATE)
        prefs.edit().putStringSet("all_save_dates", allDates).apply()

        // Log for debugging
        Log.d("MainActivity", "Saved mood to shared preferences: $text for date $dateStr")
        Log.d("MainActivity", "Updated all save dates: $allDates")
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        saveDateManager = SaveDateManager(this)

        // Restore the current screen from saved instance state if available
        if (savedInstanceState != null) {
            currentScreen = savedInstanceState.getString("CURRENT_SCREEN", "start")
            recordedAudioPath = savedInstanceState.getString("RECORDED_AUDIO_PATH")
            recordedVideoPath = savedInstanceState.getString("RECORDED_VIDEO_PATH")
            Log.d("MainActivity", "Restored screen from saved state: $currentScreen")
        } else {
            // Check if we have a screen to navigate to from the intent
            val screenFromIntent = intent.getStringExtra("SCREEN")
            if (screenFromIntent != null) {
                Log.d("MainActivity", "Found screen in intent: $screenFromIntent")
                currentScreen = screenFromIntent
            }
        }

        setContent {
            val themeManager = ThemeManager.getInstance(this)
            val currentTheme by themeManager.currentTheme

            // Update system UI colors when theme changes
            LaunchedEffect(currentTheme) {
                updateSystemUIColors(currentTheme)
            }

            OneLineDiaryTheme(appTheme = currentTheme) {
                App(
                    saveText = { text -> saveToPhone(text, ContentType.TEXT) },
                    savePhoto = { text -> savePhoto(text) },
                    saveVideo = { text -> saveVideo(text) },
                    saveAudio = { text -> saveAudio(text) },
                    saveMood = { text -> saveMood(text) },
                    saveDateManager = saveDateManager
                )
            }
        }
    }

    // Handle activity results, especially for video recording
    @Suppress("DEPRECATION")
    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        Log.d("MainActivity", "onActivityResult: requestCode=$requestCode, resultCode=$resultCode")

        // Handle video recording result (request code 1001)
        if (requestCode == 1001) {
            if (resultCode == RESULT_OK) {
                Log.d("MainActivity", "Video recording successful")

                // The video file path should already be set in the NewMainMenuScreen
                // when the recording intent was launched, but we need to verify the file exists
                val videoFile = recordedVideoPath?.let { File(it) }

                if (videoFile != null && videoFile.exists() && videoFile.length() > 0) {
                    Log.d("MainActivity", "Video file exists and has content: ${videoFile.absolutePath}")

                    // Use MediaScannerConnection for modern media scanning
                    MediaScannerConnection.scanFile(
                        this,
                        arrayOf(videoFile.absolutePath),
                        arrayOf("video/mp4"),
                        null
                    )

                    // Set currentEditingMedia to "video" to show the video player and save/wipe buttons
                    val sharedPrefs = getSharedPreferences("temp_prefs", Context.MODE_PRIVATE)
                    sharedPrefs.edit().putString("current_editing_media", "video").apply()
                } else {
                    Log.e("MainActivity", "Video file is missing or empty: ${recordedVideoPath}")
                    Toast.makeText(this, "Video recording failed: File is missing or empty", Toast.LENGTH_SHORT).show()
                    recordedVideoPath = null
                }
            } else {
                Log.d("MainActivity", "Video recording cancelled or failed")
                // Clear the recorded video path
                recordedVideoPath = null
            }

            // IMPORTANT: We need to ensure the theme is properly applied
            // This is necessary because the system camera app may change the theme of our app
            Log.d("MainActivity", "Setting content with proper theme application")

            // First update the current screen
            currentScreen = "new_main_menu"

            // Use runOnUiThread to ensure we're on the main thread
            runOnUiThread {
                // Instead of recreating the activity, just set the content with the proper theme
                // This preserves the current state while ensuring the theme is applied
                setContent {
                    // Get the current theme from ThemeManager
                    val themeManager = ThemeManager.getInstance(this@MainActivity)
                    val currentTheme = themeManager.currentTheme.value

                    OneLineDiaryTheme(appTheme = currentTheme) {
                        App(
                            saveText = { text -> saveToPhone(text, ContentType.TEXT) },
                            savePhoto = { text -> savePhoto(text) },
                            saveVideo = { text -> saveVideo(text) },
                            saveAudio = { text -> saveAudio(text) },
                            saveMood = { text -> saveMood(text) },
                            saveDateManager = saveDateManager
                        )
                    }
                }
            }
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        // Save the current screen state to preserve it during configuration changes
        outState.putString("CURRENT_SCREEN", currentScreen)
        outState.putString("RECORDED_AUDIO_PATH", recordedAudioPath)
        outState.putString("RECORDED_VIDEO_PATH", recordedVideoPath)
        Log.d("MainActivity", "Saved screen state: $currentScreen")
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        Log.d("MainActivity", "Configuration changed, current screen: $currentScreen")

        // Check if user tried to rotate to landscape
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            // Show message that landscape is not supported
            Toast.makeText(this, "Landscape mode is not supported. Please use portrait mode.", Toast.LENGTH_LONG).show()
            Log.d("MainActivity", "User attempted to rotate to landscape - showing message")
        }
    }
}

@Composable
fun App(
    saveText: (String) -> Unit,
    savePhoto: (String) -> Unit,
    saveVideo: (String) -> Unit,
    saveAudio: (String) -> Unit,
    saveMood: (String) -> Unit,
    saveDateManager: SaveDateManager
) {
    val context = LocalContext.current
    val activity = LocalContext.current as MainActivity
    val currentScreen = activity.currentScreen // Use the MainActivity's currentScreen
    var recordedAudioPath by remember { mutableStateOf<String?>(null) }
    var recordedVideoPath by remember { mutableStateOf<String?>(null) }
    var selectedDate by remember { mutableStateOf<Calendar?>(null) }

    // Wrap all screens in the OneLineDiaryTheme to ensure consistent styling
    // Get the current theme from ThemeManager - use by to make it reactive to changes
    val themeManager = remember { ThemeManager.getInstance(context) }
    val currentTheme by themeManager.currentTheme

    // Update system UI colors when theme changes (for when App is called directly)
    LaunchedEffect(currentTheme) {
        if (context is MainActivity) {
            context.updateSystemUIColors(currentTheme)
        }
    }

    OneLineDiaryTheme(appTheme = currentTheme) {
        // Get the background color from the theme
        val backgroundColor = MaterialTheme.colorScheme.background

        // Add a Box with the background color to ensure it's applied to all screens
        // Use fillMaxSize and background to ensure the color is applied consistently
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(backgroundColor)
        ) {
            when (currentScreen) {
            "start" -> StartScreen(
                onNavigate = { activity.navigateToScreen("new_main_menu") },
                onGoToNewMenu = { activity.navigateToScreen("new_main_menu") }
            )
        "new_main_menu" -> NewMainMenuScreen(
            onBack = {
                // We'll handle this in the NewMainMenuScreen with the save dialog if needed
                activity.navigateToScreen("start")
            },
            onText = { /* Handled internally in NewMainMenuScreen */ },
            onPhoto = { /* Handled internally in NewMainMenuScreen */ },
            onVideo = { /* Handled internally in NewMainMenuScreen */ },
            onAudio = { /* Handled internally in NewMainMenuScreen */ },
            onHistory = { activity.navigateToScreen("history") },
            onSettings = { activity.navigateToScreen("settings") },
            onSaveText = { text ->
                activity.saveToPhone(text, ContentType.TEXT)
                // No need to navigate away, we'll show success screen in place
            },
            savePhoto = { photoInfo ->
                // Save photo info to shared preferences and file
                val sharedPrefs = context.getSharedPreferences("diary_prefs", Context.MODE_PRIVATE)
                val currentDate = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
                sharedPrefs.edit().putString("photo_$currentDate", photoInfo).apply()
                Log.d("MainActivity", "Saved photo info: $photoInfo")

                // Also save to file system and record in SaveDateManager
                activity.savePhoto(photoInfo)
            },
            saveVideo = { videoInfo ->
                // Save video info to shared preferences and file
                val sharedPrefs = context.getSharedPreferences("diary_prefs", Context.MODE_PRIVATE)
                val currentDate = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
                sharedPrefs.edit().putString("video_$currentDate", videoInfo).apply()
                Log.d("MainActivity", "Saved video info: $videoInfo")

                // Also save to file system and record in SaveDateManager
                activity.saveVideo(videoInfo)
            },
            saveAudio = { audioInfo ->
                // Save audio info to shared preferences and file
                val sharedPrefs = context.getSharedPreferences("diary_prefs", Context.MODE_PRIVATE)
                val currentDate = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
                sharedPrefs.edit().putString("audio_$currentDate", audioInfo).apply()
                Log.d("MainActivity", "Saved audio info: $audioInfo")

                // Also save to file system and record in SaveDateManager
                activity.saveAudio(audioInfo)
            },
            saveMood = { moodInfo ->
                // Save mood info and record that mood has been saved today
                activity.saveMood(moodInfo)
            },
            saveDateManager = saveDateManager
        )
        // DailySentenceScreen has been removed as its functionality is now in NewMainMenuScreen
        "history" -> HistoryScreen(
            onBack = { activity.navigateToScreen("new_main_menu") },
            onDaySelected = { date ->
                Log.d("MainActivity", "Day selected in HistoryScreen: ${SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(date.time)}")
                // Store the selected date in both the activity and the local state
                activity.selectedHistoryDate = date
                selectedDate = date
                activity.navigateToScreen("history_day")
            },
            onDownload = {
                Log.d("MainActivity", "HistoryScreen download button pressed, using direct method")
                activity.navigateToDownload()
            },
            saveDateManager = saveDateManager
        )
        "history_day" -> {
            Log.d("MainActivity", "Entering history_day screen")
            // Use the activity's stored date if available, otherwise fall back to the local state
            val dateToUse = activity.selectedHistoryDate ?: selectedDate

            dateToUse?.let { date ->
                Log.d("MainActivity", "Selected date is not null: ${SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(date.time)}")
                HistoryDayScreen(
                    date = date,
                    onBack = {
                        Log.d("MainActivity", "HistoryDayScreen onBack called, using direct method")
                        // Use the direct method for more reliable navigation
                        activity.forceNavigateToHistoryFromDay()
                    },
                    saveDateManager = saveDateManager
                )
            } ?: run {
                // Fallback if date is null
                Log.d("MainActivity", "Selected date is null, navigating back to history screen")
                activity.forceNavigateToHistoryFromDay()
            }
        }
        "video_recorder" -> VideoRecorderScreen(
            onRecordingComplete = { path ->
                recordedVideoPath = path
                // Use navigateToNewMainMenu to ensure proper theme application
                activity.navigateToNewMainMenu()
            }
        )
        "audio_recorder" -> AudioRecorderScreen(
            onRecordingComplete = { path ->
                recordedAudioPath = path
                // Store the path in MainActivity for access by NewMainMenuScreen
                (activity as MainActivity).recordedAudioPath = path
                // Use navigateToNewMainMenu to ensure proper theme application
                activity.navigateToNewMainMenu()
            }
        )
        "download" -> DownloadScreen(
            onBack = {
                Log.d("MainActivity", "DownloadScreen back button pressed, navigating to settings")
                activity.navigateToScreen("settings")
            },
            saveDateManager = saveDateManager
        )
        "settings" -> SettingsScreen(
            onBack = {
                Log.d("MainActivity", "SettingsScreen back button pressed, using direct method")
                activity.navigateToNewMainMenu()
            },
            onDownload = {
                Log.d("MainActivity", "SettingsScreen download button pressed, using direct method")
                activity.navigateToDownload()
            },
            saveDateManager = saveDateManager
        )
            }
        }
    }
}

// Helper function to save bitmap to file and get its URI
private fun saveBitmapToFile(context: Context, bitmap: Bitmap): Uri? {
    return try {
        // Create a file to save the image
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        val imageFile = File.createTempFile("PHOTO_${timestamp}_", ".jpg", storageDir)

        // Save the bitmap to the file
        val outputStream = FileOutputStream(imageFile)
        bitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
        outputStream.flush()
        outputStream.close()

        // Get the URI using FileProvider
        FileProvider.getUriForFile(
            context,
            "${context.packageName}.provider",
            imageFile
        )
    } catch (e: Exception) {
        Log.e("DailyPhotoScreen", "Error saving bitmap to file", e)
        null
    }
}

// Helper function to create a file for video recording in the main menu
private fun createVideoFileForMainMenu(context: Context): File? {
    return try {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_MOVIES)
        File.createTempFile("VIDEO_${timestamp}_", ".mp4", storageDir)
    } catch (e: IOException) {
        Log.e("NewMainMenuScreen", "Error creating video file", e)
        Toast.makeText(context, "Error creating video file", Toast.LENGTH_SHORT).show()
        null
    }
}

// Helper function to create a file for audio recording in the main menu
private fun createAudioFileForMainMenu(context: Context): File? {
    return try {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_MUSIC)
        File.createTempFile("AUDIO_${timestamp}_", ".3gp", storageDir)
    } catch (e: IOException) {
        Log.e("NewMainMenuScreen", "Error creating audio file", e)
        Toast.makeText(context, "Error creating audio file", Toast.LENGTH_SHORT).show()
        null
    }
}

// DailyPhotoScreen has been removed as its functionality is now in NewMainMenuScreen

// DailyAudioScreen has been removed as its functionality is now in NewMainMenuScreen

@Composable
fun AudioRecorderScreen(
    onRecordingComplete: (String) -> Unit
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()

    // State variables
    var isRecording by remember { mutableStateOf(false) }
    var timeElapsed by remember { mutableStateOf(0) }
    var recorder by remember { mutableStateOf<MediaRecorder?>(null) }
    var outputFile by remember { mutableStateOf<File?>(null) }
    var hasAudioPermission by remember { mutableStateOf(false) }
    var showPermissionDialog by remember { mutableStateOf(false) }

    // Permission launcher
    val requestPermissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        hasAudioPermission = isGranted
        if (isGranted) {
            // Permission granted, create file and start recording
            createOutputFile(context)?.let { file ->
                outputFile = file
                startRecording(context, file.absolutePath)?.let { newRecorder ->
                    recorder = newRecorder
                    isRecording = true
                }
            }
        } else {
            // Permission denied
            showPermissionDialog = true
            Toast.makeText(
                context,
                "Microphone permission is required to record audio",
                Toast.LENGTH_LONG
            ).show()
        }
    }

    // Check if we already have permission
    LaunchedEffect(Unit) {
        hasAudioPermission = ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED

        // Create output file
        createOutputFile(context)?.let { file ->
            outputFile = file
        }
    }

    // Permission dialog
    if (showPermissionDialog) {
        AlertDialog(
            onDismissRequest = { showPermissionDialog = false },
            title = { Text("Permission Required") },
            text = { Text("Microphone permission is required to record audio. Please grant this permission in your device settings.") },
            confirmButton = {
                TextButton(onClick = { showPermissionDialog = false }) {
                    Text("OK")
                }
            }
        )
    }

    // Timer for recording duration
    LaunchedEffect(isRecording) {
        if (isRecording) {
            timeElapsed = 0
            while (isRecording && timeElapsed < 15) {
                delay(1000)
                timeElapsed++

                // Auto-stop after 15 seconds
                if (timeElapsed >= 15) {
                    stopRecording(recorder, outputFile?.absolutePath)?.let { path ->
                        onRecordingComplete(path)
                    }
                    isRecording = false
                    recorder = null
                }
            }
        }
    }

    // Clean up when leaving the screen
    DisposableEffect(Unit) {
        onDispose {
            recorder?.apply {
                try {
                    if (isRecording) {
                        stop()
                        release()
                    }
                } catch (e: Exception) {
                    Log.e("AudioRecorderScreen", "Error releasing recorder", e)
                }
            }
            recorder = null
        }
    }

    // Get the background color from the theme
    val backgroundColor = MaterialTheme.colorScheme.background

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
        Text(
            "Audio Recorder",
            style = MaterialTheme.typography.headlineMedium
        )

        Spacer(modifier = Modifier.height(32.dp))

        // Recording indicator and timer
        if (isRecording) {
            Text(
                "Recording... ${15 - timeElapsed}s left",
                style = MaterialTheme.typography.titleLarge,
                color = MaterialTheme.colorScheme.error
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Progress indicator
            LinearProgressIndicator(
                progress = { timeElapsed / 15f },
                modifier = Modifier.fillMaxWidth()
            )
        } else {
            Text(
                if (hasAudioPermission)
                    "Press the button to start recording"
                else
                    "Microphone permission needed",
                style = MaterialTheme.typography.bodyLarge
            )
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Record/Stop button
        Button(
            onClick = {
                if (isRecording) {
                    // Stop recording
                    stopRecording(recorder, outputFile?.absolutePath)?.let { path ->
                        onRecordingComplete(path)
                    }
                    isRecording = false
                    recorder = null
                } else {
                    // Check permission and start recording
                    if (hasAudioPermission) {
                        // We have permission, start recording
                        startRecording(context, outputFile?.absolutePath)?.let { newRecorder ->
                            recorder = newRecorder
                            isRecording = true
                        }
                    } else {
                        // Request permission
                        requestPermissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
                    }
                }
            },
            modifier = Modifier.size(100.dp),
            shape = androidx.compose.foundation.shape.CircleShape,
            colors = ButtonDefaults.buttonColors(
                containerColor = if (isRecording) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.primary
            )
        ) {
            Text(
                if (isRecording) "Stop" else "Record",
                style = MaterialTheme.typography.titleMedium
            )
        }

        // Spacer at the bottom for better layout
        Spacer(modifier = Modifier.height(32.dp))
        }
    }
}

// Helper function to create output file
private fun createOutputFile(context: Context): File? {
    return try {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_MUSIC)

        // Make sure the directory exists
        if (storageDir?.exists() != true) {
            storageDir?.mkdirs()
        }

        // Try to create a file with .m4a extension (AAC format) which is more widely supported
        val file = File.createTempFile("AUDIO_${timestamp}_", ".m4a", storageDir)
        Log.d("AudioRecorderScreen", "Created audio file: ${file.absolutePath}")
        file
    } catch (e: IOException) {
        Log.e("AudioRecorderScreen", "Error creating output file", e)
        Toast.makeText(context, "Error creating output file", Toast.LENGTH_SHORT).show()
        null
    }
}

// Helper function to start recording
private fun startRecording(context: Context, filePath: String?): MediaRecorder? {
    if (filePath == null) {
        Toast.makeText(context, "Cannot create output file", Toast.LENGTH_SHORT).show()
        return null
    }

    Log.d("AudioRecorderScreen", "Starting recording to file: $filePath")

    return try {
        // Create MediaRecorder with appropriate API level handling
        val recorder = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            MediaRecorder(context)
        } else {
            @Suppress("DEPRECATION")
            MediaRecorder()
        }

        try {
            // Configure the recorder with error handling at each step
            recorder.apply {
                try {
                    setAudioSource(MediaRecorder.AudioSource.MIC)
                    Log.d("AudioRecorderScreen", "Set audio source to MIC")
                } catch (e: Exception) {
                    Log.e("AudioRecorderScreen", "Error setting audio source", e)
                    Toast.makeText(context, "Error setting audio source: ${e.message}", Toast.LENGTH_SHORT).show()
                    throw e
                }

                try {
                    // Use MPEG_4 format which is more widely supported
                    setOutputFormat(MediaRecorder.OutputFormat.MPEG_4)
                    Log.d("AudioRecorderScreen", "Set output format to MPEG_4")
                } catch (e: Exception) {
                    Log.e("AudioRecorderScreen", "Error setting output format", e)
                    Toast.makeText(context, "Error setting output format: ${e.message}", Toast.LENGTH_SHORT).show()
                    throw e
                }

                try {
                    // Use AAC encoder which is more widely supported
                    setAudioEncoder(MediaRecorder.AudioEncoder.AAC)
                    Log.d("AudioRecorderScreen", "Set audio encoder to AAC")
                } catch (e: Exception) {
                    Log.e("AudioRecorderScreen", "Error setting audio encoder", e)
                    Toast.makeText(context, "Error setting audio encoder: ${e.message}", Toast.LENGTH_SHORT).show()
                    throw e
                }

                try {
                    setOutputFile(filePath)
                    Log.d("AudioRecorderScreen", "Set output file to: $filePath")
                } catch (e: Exception) {
                    Log.e("AudioRecorderScreen", "Error setting output file", e)
                    Toast.makeText(context, "Error setting output file: ${e.message}", Toast.LENGTH_SHORT).show()
                    throw e
                }

                // Try to prepare the recorder
                try {
                    prepare()
                    Log.d("AudioRecorderScreen", "Recorder prepared successfully")
                } catch (e: Exception) {
                    Log.e("AudioRecorderScreen", "Error preparing recorder, trying alternative settings", e)

                    // Reset and try with different settings
                    reset()
                    setAudioSource(MediaRecorder.AudioSource.CAMCORDER) // Try alternative source
                    setOutputFormat(MediaRecorder.OutputFormat.THREE_GPP)  // Try alternative format
                    setAudioEncoder(MediaRecorder.AudioEncoder.AMR_NB)     // Try alternative encoder
                    setOutputFile(filePath)
                    Log.d("AudioRecorderScreen", "Trying alternative settings: CAMCORDER source, THREE_GPP format, AMR_NB encoder")

                    try {
                        prepare()
                        Log.d("AudioRecorderScreen", "Recorder prepared successfully with alternative settings")
                    } catch (e2: Exception) {
                        Log.e("AudioRecorderScreen", "Error preparing recorder with alternative settings", e2)
                        Toast.makeText(context, "Could not prepare audio recorder", Toast.LENGTH_SHORT).show()
                        throw e2
                    }
                }

                try {
                    start()
                    Log.d("AudioRecorderScreen", "Recording started")
                } catch (e: Exception) {
                    Log.e("AudioRecorderScreen", "Error starting recorder", e)
                    Toast.makeText(context, "Error starting recorder: ${e.message}", Toast.LENGTH_SHORT).show()
                    throw e
                }
            }

            recorder
        } catch (e: Exception) {
            // Clean up on error
            try {
                recorder.release()
                Log.d("AudioRecorderScreen", "Released recorder after error")
            } catch (releaseError: Exception) {
                Log.e("AudioRecorderScreen", "Error releasing recorder after failure", releaseError)
            }

            Toast.makeText(
                context,
                "Could not start recording: ${e.message}",
                Toast.LENGTH_SHORT
            ).show()
            null
        }
    } catch (e: Exception) {
        Log.e("AudioRecorderScreen", "Error creating MediaRecorder", e)
        Toast.makeText(context, "Error creating recorder: ${e.message}", Toast.LENGTH_SHORT).show()
        null
    }
}

// Helper function to stop recording
private fun stopRecording(recorder: MediaRecorder?, filePath: String?): String? {
    if (recorder == null || filePath == null) {
        Log.e("AudioRecorderScreen", "Cannot stop recording: recorder or filePath is null")
        return null
    }

    Log.d("AudioRecorderScreen", "Stopping recording to file: $filePath")

    return try {
        try {
            recorder.stop()
            Log.d("AudioRecorderScreen", "Recording stopped successfully")
        } catch (e: Exception) {
            // Ignore stop errors, as they can happen if recording is too short
            Log.w("AudioRecorderScreen", "Warning stopping recorder (may be normal for short recordings)", e)
        }

        try {
            recorder.release()
            Log.d("AudioRecorderScreen", "Recorder released successfully")
        } catch (e: Exception) {
            Log.e("AudioRecorderScreen", "Error releasing recorder", e)
        }

        // Check if the file exists and has content
        val file = File(filePath)
        if (file.exists() && file.length() > 0) {
            Log.d("AudioRecorderScreen", "Audio file saved successfully: ${file.absolutePath} (size: ${file.length()} bytes)")

            // Note: Media scanner notification is handled at a higher level
            // The file will still be accessible, but might not show up in media library immediately
            Log.d("AudioRecorderScreen", "Audio file created successfully")

            // Return the file path
            filePath
        } else {
            Log.e("AudioRecorderScreen", "Audio file is missing or empty: $filePath")
            null
        }
    } catch (e: Exception) {
        Log.e("AudioRecorderScreen", "Fatal error stopping recording", e)
        null
    }
}

// DailyVideoScreen has been removed as its functionality is now in NewMainMenuScreen


@Composable
fun VideoRecorderScreen(
    onRecordingComplete: (String) -> Unit
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()

    // State variables
    var isRecording by remember { mutableStateOf(false) }
    var timeElapsed by remember { mutableStateOf(0) }
    var outputFile by remember { mutableStateOf<File?>(null) }
    var videoUri by remember { mutableStateOf<Uri?>(null) }
    var hasVideoPermissions by remember { mutableStateOf(false) }
    var showPermissionDialog by remember { mutableStateOf(false) }

    // Permissions needed for video recording
    val requiredPermissions = remember {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arrayOf(
                Manifest.permission.CAMERA,
                Manifest.permission.RECORD_AUDIO
            )
        } else {
            arrayOf(
                Manifest.permission.CAMERA,
                Manifest.permission.RECORD_AUDIO,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            )
        }
    }

    // Check if we have all required permissions
    fun checkPermissions(): Boolean {
        return requiredPermissions.all {
            ContextCompat.checkSelfPermission(context, it) == PackageManager.PERMISSION_GRANTED
        }
    }

    // Permission launcher
    val requestPermissionsLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        hasVideoPermissions = permissions.values.all { it }
        if (hasVideoPermissions) {
            // Permissions granted, create file and prepare for recording
            createVideoFile(context)?.let { file ->
                outputFile = file
                videoUri = FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.provider",
                    file
                )
                Log.d("VideoRecorderScreen", "Created video file: ${file.absolutePath}")
                Log.d("VideoRecorderScreen", "Video URI: $videoUri")
            }
        } else {
            // Some permissions denied
            showPermissionDialog = true
            Toast.makeText(
                context,
                "Camera and microphone permissions are required to record video",
                Toast.LENGTH_LONG
            ).show()
        }
    }

    // Video recording launcher
    val takeVideoLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.CaptureVideo()
    ) { success ->
        isRecording = false
        if (success) {
            // Check if the file exists and has content
            val file = outputFile
            if (file != null && file.exists() && file.length() > 0) {
                Log.d("VideoRecorderScreen", "Video recorded successfully: ${file.absolutePath}")
                Log.d("VideoRecorderScreen", "File size: ${file.length()} bytes")

                // Use MediaScannerConnection for modern media scanning
                MediaScannerConnection.scanFile(
                    context,
                    arrayOf(file.absolutePath),
                    arrayOf("video/mp4"),
                    null
                )

                onRecordingComplete(file.absolutePath)
            } else {
                Log.e("VideoRecorderScreen", "Video file is missing or empty: ${file?.absolutePath}")
                Toast.makeText(context, "Video recording failed: File is missing or empty", Toast.LENGTH_SHORT).show()
            }
        } else {
            Log.e("VideoRecorderScreen", "Video recording failed")
            Toast.makeText(context, "Video recording failed", Toast.LENGTH_SHORT).show()
        }
    }

    // Check permissions on first launch
    LaunchedEffect(Unit) {
        hasVideoPermissions = checkPermissions()
        if (!hasVideoPermissions) {
            requestPermissionsLauncher.launch(requiredPermissions)
        } else {
            // Create output file
            createVideoFile(context)?.let { file ->
                outputFile = file
                videoUri = FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.provider",
                    file
                )
                Log.d("VideoRecorderScreen", "Created video file: ${file.absolutePath}")
                Log.d("VideoRecorderScreen", "Video URI: $videoUri")
            }
        }
    }

    // Timer for recording duration
    LaunchedEffect(isRecording) {
        if (isRecording) {
            timeElapsed = 0
            while (isRecording && timeElapsed < 15) {
                delay(1000)
                timeElapsed++

                // Auto-stop after 15 seconds
                if (timeElapsed >= 15) {
                    // The video recording is handled by the system camera app,
                    // so we can't directly stop it. The user will need to stop manually.
                    isRecording = false
                }
            }
        }
    }

    // Permission dialog
    if (showPermissionDialog) {
        AlertDialog(
            onDismissRequest = { showPermissionDialog = false },
            title = { Text("Permissions Required") },
            text = { Text("Camera and microphone permissions are required to record video. Please grant these permissions in your device settings.") },
            confirmButton = {
                TextButton(onClick = { showPermissionDialog = false }) {
                    Text("OK")
                }
            }
        )
    }

    // Get the background color from the theme
    val backgroundColor = MaterialTheme.colorScheme.background

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
        Text(
            "Video Recorder",
            style = MaterialTheme.typography.headlineMedium
        )

        Spacer(modifier = Modifier.height(32.dp))

        // Recording indicator and instructions
        if (isRecording) {
            Text(
                "Recording in progress...",
                style = MaterialTheme.typography.titleLarge,
                color = MaterialTheme.colorScheme.error
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                "Maximum recording time: 15 seconds",
                style = MaterialTheme.typography.bodyMedium
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Timer display
            Text(
                "Time elapsed: $timeElapsed / 15 seconds",
                style = MaterialTheme.typography.bodyLarge
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Progress indicator
            LinearProgressIndicator(
                progress = { timeElapsed / 15f },
                modifier = Modifier.fillMaxWidth()
            )
        } else {
            Text(
                if (hasVideoPermissions)
                    "Press the button to start recording"
                else
                    "Camera and microphone permissions needed",
                style = MaterialTheme.typography.bodyLarge
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                "Maximum recording time: 15 seconds",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Record button
        Button(
            onClick = {
                if (hasVideoPermissions && videoUri != null) {
                    isRecording = true

                    // Launch the system camera app to record video
                    // Use a local variable to avoid smart cast issue with delegated property
                    val uri = videoUri
                    if (uri != null) {
                        Log.d("VideoRecorderScreen", "Launching video recording with URI: $uri")
                        takeVideoLauncher.launch(uri)
                    }
                } else {
                    // Request permissions
                    requestPermissionsLauncher.launch(requiredPermissions)
                }
            },
            modifier = Modifier.size(100.dp),
            shape = androidx.compose.foundation.shape.CircleShape,
            colors = ButtonDefaults.buttonColors(
                containerColor = if (isRecording) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.primary
            ),
            enabled = !isRecording && hasVideoPermissions
        ) {
            Text(
                "Record",
                style = MaterialTheme.typography.titleMedium
            )
        }

        // Spacer at the bottom for better layout
        Spacer(modifier = Modifier.height(32.dp))
        }
    }
}

// Helper function to create a video file
private fun createVideoFile(context: Context): File? {
    return try {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_MOVIES)
        File.createTempFile("VIDEO_${timestamp}_", ".mp4", storageDir)
    } catch (e: IOException) {
        Log.e("VideoRecorderScreen", "Error creating video file", e)
        Toast.makeText(context, "Error creating video file", Toast.LENGTH_SHORT).show()
        null
    }
}

@Composable
fun StartScreen(onNavigate: () -> Unit, onGoToNewMenu: () -> Unit = {}) {
    // Get colors from the current theme
    val backgroundColor = MaterialTheme.colorScheme.background
    val buttonColor = MaterialTheme.colorScheme.primary
    val textColor = MaterialTheme.colorScheme.onBackground
    val accentColor = MaterialTheme.colorScheme.primary
    // Accent color for disabled states - theme aware
    val orangeAccent = getSoftPastelColor() // Soft pastel color for disabled states
    val darkerBackground = textColor.copy(alpha = 0.8f)

    val context = LocalContext.current

    // Get shared preferences to check if user has entered name before
    val prefs = context.getSharedPreferences("user_prefs", Context.MODE_PRIVATE)
    val savedName = prefs.getString("user_name", null)

    // State for user name input
    var userName by remember { mutableStateOf(savedName ?: "") }
    var isFirstTime by remember { mutableStateOf(savedName == null) }
    var nameSubmitted by remember { mutableStateOf(savedName != null) }

    // State for animation
    var showContent by remember { mutableStateOf(false) }

    // Animation effect when the screen appears
    LaunchedEffect(Unit) {
        delay(100)
        showContent = true
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
    ) {
        // Content with animations
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(24.dp),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Animated title
            AnimatedVisibility(
                visible = showContent,
                enter = fadeIn() + slideInVertically { -50 }
            ) {
                Text(
                    text = "One Line Diary",
                    style = MaterialTheme.typography.headlineLarge.copy(
                        fontFamily = FontFamily.Serif,
                        fontWeight = FontWeight.Bold,
                        color = accentColor // Use accent color to match "See you tomorrow" button
                    ),
                    textAlign = TextAlign.Center
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Animated subtitle
            AnimatedVisibility(
                visible = showContent,
                enter = fadeIn() + slideInVertically { -30 }
            ) {
                Text(
                    text = "Capture your day in a single line",
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontFamily = FontFamily.Serif,
                        fontStyle = FontStyle.Italic,
                        color = textColor.copy(alpha = 0.8f)
                    ),
                    textAlign = TextAlign.Center
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Show different content based on whether it's first time or not
            AnimatedVisibility(
                visible = showContent,
                enter = fadeIn() + slideInVertically { -30 }
            ) {
                if (isFirstTime && !nameSubmitted) {
                    // First time - ask for name
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "Welcome! Please enter your name:",
                            style = MaterialTheme.typography.bodyLarge.copy(
                                fontFamily = FontFamily.Serif,
                                color = textColor
                            ),
                            textAlign = TextAlign.Center
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        // Name input field
                        OutlinedTextField(
                            value = userName,
                            onValueChange = { userName = it },
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 32.dp),
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = accentColor,
                                unfocusedBorderColor = textColor.copy(alpha = 0.5f),
                                focusedContainerColor = Color.White.copy(alpha = 0.9f),
                                unfocusedContainerColor = Color.White.copy(alpha = 0.7f)
                            ),
                            textStyle = MaterialTheme.typography.bodyLarge.copy(
                                color = textColor
                            ),
                            singleLine = true,
                            placeholder = { Text("Your name") }
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        // Submit button
                        Button(
                            onClick = {
                                if (userName.isNotBlank()) {
                                    // Save name to preferences
                                    prefs.edit().putString("user_name", userName).apply()
                                    nameSubmitted = true
                                    Log.d("StartScreen", "Name saved: $userName")
                                } else {
                                    // Show toast if name is empty
                                    Toast.makeText(context, "Please enter your name", Toast.LENGTH_SHORT).show()
                                }
                            },
                            modifier = Modifier
                                .padding(horizontal = 32.dp)
                                .fillMaxWidth()
                                .height(56.dp),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = accentColor
                            ),
                            shape = RoundedCornerShape(12.dp),
                            elevation = ButtonDefaults.buttonElevation(defaultElevation = 4.dp)
                        ) {
                            Text(
                                "Submit",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.Medium,
                                    color = MaterialTheme.colorScheme.onPrimary
                                )
                            )
                        }
                    }
                } else {
                    // Returning user - show welcome back message
                    Text(
                        text = "Welcome back, $userName!",
                        style = MaterialTheme.typography.headlineSmall.copy(
                            fontFamily = FontFamily.Serif,
                            fontWeight = FontWeight.Medium,
                            color = textColor
                        ),
                        textAlign = TextAlign.Center
                    )
                }
            }

            Spacer(modifier = Modifier.height(48.dp))

            // Animated button - only show if name is submitted or not first time
            AnimatedVisibility(
                visible = showContent && (nameSubmitted || !isFirstTime),
                enter = fadeIn() + slideInVertically { 50 }
            ) {
                Button(
                    onClick = onGoToNewMenu,
                    modifier = Modifier
                        .padding(horizontal = 32.dp)
                        .fillMaxWidth()
                        .height(56.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = accentColor
                    ),
                    shape = RoundedCornerShape(12.dp),
                    elevation = ButtonDefaults.buttonElevation(defaultElevation = 4.dp)
                ) {
                    Text(
                        "Begin Your Journey",
                        style = MaterialTheme.typography.titleMedium.copy(
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.onPrimary
                        )
                    )
                }
            }
        }
    }
}

@Composable
fun MediaSelectionScreen(
    onConfirm: (Set<String>) -> Unit
) {
    val options = listOf("Text", "Photo", "Video", "Audio")
    val selectedOptions = remember { mutableStateOf(mutableSetOf<String>()) }
    val sortedOptions = selectedOptions.value.sorted()

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        options.forEach { option ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Checkbox(
                    checked = selectedOptions.value.contains(option),
                    onCheckedChange = { isChecked ->
                        val newSet = selectedOptions.value.toMutableSet()
                        if (isChecked) {
                            newSet.add(option)
                        } else {
                            newSet.remove(option)
                        }
                        selectedOptions.value = newSet
                    },
                    colors = CheckboxDefaults.colors(
                        checkedColor = MaterialTheme.colorScheme.primary,
                        uncheckedColor = MaterialTheme.colorScheme.onSurfaceVariant,
                        checkmarkColor = MaterialTheme.colorScheme.onPrimary
                    )
                )
                Text(
                    text = option,
                    modifier = Modifier.padding(start = 8.dp),
                    color = if (selectedOptions.value.contains(option)) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurface,
                    style = if (selectedOptions.value.contains(option)) MaterialTheme.typography.bodyLarge else MaterialTheme.typography.bodyMedium
                )
            }
        }
        Spacer(modifier = Modifier.height(16.dp))
        Button(
            onClick = { onConfirm(selectedOptions.value) },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("Confirm")
        }
    }
}



// Using the OneLineDiaryTheme from ui.theme package

@Composable
fun NewMainMenuScreen(
    onBack: () -> Unit,
    onText: () -> Unit,
    onPhoto: () -> Unit,
    onVideo: () -> Unit,
    onAudio: () -> Unit,
    onHistory: () -> Unit,
    onSettings: () -> Unit,
    onSaveText: (String) -> Unit = {},
    savePhoto: (String) -> Unit = {},
    saveVideo: (String) -> Unit = {},
    saveAudio: (String) -> Unit = {},
    saveMood: (String) -> Unit = {},
    saveDateManager: SaveDateManager = SaveDateManager(LocalContext.current)
) {
    val context = LocalContext.current
    val activity = context as? Activity
    // Add coroutineScope for launching coroutines from composable functions
    val coroutineScope = rememberCoroutineScope()

    // Define a consistent height for all media content
    val mediaContentHeight = 250.dp

    // State for text input and media editing
    var text by remember { mutableStateOf("") }
    var showSuccessScreen by remember { mutableStateOf(false) }
    var textSavedInSession by remember { mutableStateOf(false) }
    var showSaveDialog by remember { mutableStateOf(false) }

    // State to track which media type is being edited (if any)
    var currentEditingMedia by remember { mutableStateOf<String?>(null) }

    // Media state variables
    var selectedImageUri by remember { mutableStateOf<Uri?>(null) }
    var capturedBitmap by remember { mutableStateOf<Bitmap?>(null) }
    var capturedImageUri by remember { mutableStateOf<Uri?>(null) }
    var selectedVideoUri by remember { mutableStateOf<Uri?>(null) }
    var localRecordedVideoPath by remember { mutableStateOf<String?>(null) }
    var selectedAudioUri by remember { mutableStateOf<Uri?>(null) }
    var localRecordedAudioPath by remember { mutableStateOf<String?>(null) }
    var isPlaying by remember { mutableStateOf(false) }
    var mediaPlayer by remember { mutableStateOf<MediaPlayer?>(null) }

    // Video playback state
    var videoPlaybackKey by remember { mutableStateOf(0) }
    var shouldPlayVideo by remember { mutableStateOf(false) }

    // Check if each content type has been saved today
    val textSavedToday = saveDateManager.hasSavedToday(ContentType.TEXT)
    val photoSavedToday = saveDateManager.hasSavedToday(ContentType.PHOTO)
    val videoSavedToday = saveDateManager.hasSavedToday(ContentType.VIDEO)
    val audioSavedToday = saveDateManager.hasSavedToday(ContentType.AUDIO)
    val moodSavedToday = saveDateManager.hasSavedToday(ContentType.MOOD)

    // Check if there's a recorded audio or video path from MainActivity
    LaunchedEffect(Unit) {
        // Check for recorded audio path
        (context as? MainActivity)?.recordedAudioPath?.let { path ->
            localRecordedAudioPath = path
            // Clear the path in MainActivity to avoid reusing it
            (context as? MainActivity)?.recordedAudioPath = null
            // Set the current editing media to audio to show the audio player
            if (!audioSavedToday) {
                currentEditingMedia = "audio"
            }
        }

        // Check for recorded video path
        (context as? MainActivity)?.recordedVideoPath?.let { path ->
            localRecordedVideoPath = path
            // Clear the path in MainActivity to avoid reusing it
            (context as? MainActivity)?.recordedVideoPath = null
            // Set the current editing media to video to show the video player
            if (!videoSavedToday) {
                currentEditingMedia = "video"
            }
        }

        // Check for current editing media preference (set in onActivityResult)
        val sharedPrefs = context.getSharedPreferences("temp_prefs", Context.MODE_PRIVATE)
        val savedEditingMedia = sharedPrefs.getString("current_editing_media", null)
        if (savedEditingMedia != null) {
            // Set the current editing media based on the preference
            currentEditingMedia = savedEditingMedia
            // Clear the preference to avoid reusing it
            sharedPrefs.edit().remove("current_editing_media").apply()
        }
    }

    // State for mood selection
    var selectedMood by remember { mutableStateOf<String?>(null) }

    // Retrieve saved mood when the screen loads
    LaunchedEffect(Unit) {
        val diaryPrefs = context.getSharedPreferences("diary_prefs", Context.MODE_PRIVATE)
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val dateStr = dateFormat.format(Date())
        val savedMoodContent = diaryPrefs.getString("mood_$dateStr", null)

        if (savedMoodContent != null) {
            // Extract the mood value from the saved content
            selectedMood = when {
                savedMoodContent.contains("Very Sad") -> "very_sad"
                savedMoodContent.contains("Sad") -> "sad"
                savedMoodContent.contains("Neutral") -> "neutral"
                savedMoodContent.contains("Happy") -> "happy"
                savedMoodContent.contains("Very Happy") -> "very_happy"
                else -> null
            }
            Log.d("NewMainMenuScreen", "Retrieved saved mood: $selectedMood from content: $savedMoodContent")
        }
    }

    // Set textSavedInSession to true if text has already been saved today
    LaunchedEffect(textSavedToday) {
        if (textSavedToday) {
            textSavedInSession = true
        }
    }

    // Get colors from the current theme
    val backgroundColor = MaterialTheme.colorScheme.background
    val buttonColor = MaterialTheme.colorScheme.primary
    val textColor = MaterialTheme.colorScheme.onBackground
    val accentColor = MaterialTheme.colorScheme.primary
    val softGreen = MaterialTheme.colorScheme.background
    val softTan = MaterialTheme.colorScheme.surface
    // Accent color for disabled states - theme aware
    val orangeAccent = getSoftPastelColor() // Soft pastel color for disabled states
    val darkerBackground = getSoftComplementaryBackgroundColor() // Soft complementary background for disabled buttons

    // Media picker launcher (gallery for both photos and videos)
    val mediaPickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let {
            val mimeType = context.contentResolver.getType(it)
            Log.d("NewMainMenuScreen", "Media selected from gallery: $it, type: $mimeType")

            when {
                mimeType?.startsWith("image/") == true -> {
                    // Handle as image
                    selectedImageUri = it
                    capturedBitmap = null // Clear any previous camera capture
                    capturedImageUri = null // Clear any previous camera capture URI
                    // Clear video state
                    selectedVideoUri = null
                    localRecordedVideoPath = null
                    // Switch to photo interface
                    currentEditingMedia = "photo"
                }
                mimeType?.startsWith("video/") == true -> {
                    // Handle as video
                    selectedVideoUri = it
                    shouldPlayVideo = false
                    localRecordedVideoPath = null // Clear any previous recording
                    videoPlaybackKey++ // Force recomposition
                    // Clear photo state
                    selectedImageUri = null
                    capturedBitmap = null
                    capturedImageUri = null
                    // Switch to video interface
                    currentEditingMedia = "video"
                }
                else -> {
                    Log.w("NewMainMenuScreen", "Unknown media type: $mimeType")
                    Toast.makeText(context, "Unsupported media type", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    // Camera launcher that returns a bitmap directly
    val takePictureLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.TakePicturePreview()
    ) { bitmap ->
        if (bitmap != null) {
            Log.d("NewMainMenuScreen", "Photo taken with camera: ${bitmap.width}x${bitmap.height}")
            capturedBitmap = bitmap
            selectedImageUri = null // Clear any previous gallery selection

            // Save the bitmap to a file and get its URI
            val uri = saveBitmapToFile(context, bitmap)
            if (uri != null) {
                capturedImageUri = uri
                Log.d("NewMainMenuScreen", "Saved captured photo to URI: $uri")
            } else {
                Log.e("NewMainMenuScreen", "Failed to save captured photo to file")
            }

            Toast.makeText(context, "Photo captured successfully", Toast.LENGTH_SHORT).show()
        } else {
            Log.e("NewMainMenuScreen", "Camera returned null bitmap")
            Toast.makeText(context, "Failed to capture photo", Toast.LENGTH_SHORT).show()
        }
    }



    // Audio picker launcher (from device)
    val audioPickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let {
            Log.d("NewMainMenuScreen", "Audio selected from device: $it")
            selectedAudioUri = it
            localRecordedAudioPath = null // Clear any previous recording
            Toast.makeText(context, "Audio file selected", Toast.LENGTH_SHORT).show()
        }
    }

    // Save dialog
    if (showSaveDialog) {
        AlertDialog(
            onDismissRequest = { showSaveDialog = false },
            title = { Text("Do you want to save?") },
            text = { Text("Your text hasn't been saved yet.") },
            confirmButton = {
                TextButton(
                    onClick = {
                        if (text.isNotEmpty()) {
                            onSaveText(text)
                            // Set text saved in session to true
                            textSavedInSession = true
                            // Clear the text after saving
                            text = ""
                        }
                        showSaveDialog = false
                        onBack()
                    }
                ) {
                    Text("Yes")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showSaveDialog = false
                        onBack()
                    }
                ) {
                    Text("No")
                }
            }
        )
    }

    Box(
            modifier = Modifier
                .fillMaxSize()
                .background(backgroundColor)
        ) {

        // Main content
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Top row with title and icons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Empty spacer to balance the layout
                Spacer(modifier = Modifier.width(80.dp))

                // Title - using serif font
                Text(
                    text = "Diary",
                    style = MaterialTheme.typography.headlineLarge.copy(
                        color = accentColor, // Use accent color to match "See you tomorrow" button
                        fontWeight = FontWeight.Bold,
                        fontFamily = FontFamily.Serif
                    )
                )

                // Icons in the top right
                Row(
                    horizontalArrangement = Arrangement.End,
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.width(80.dp)
                ) {
                    // Calendar icon
                    IconButton(
                        onClick = {
                            Log.d("NewMainMenuScreen", "Calendar icon clicked, navigating to history")
                            // Get the MainActivity directly and use the direct method
                            val mainActivity = context as? MainActivity
                            if (mainActivity != null) {
                                Log.d("NewMainMenuScreen", "Using direct navigation method to history")
                                mainActivity.navigateToHistory()
                            } else {
                                Log.e("NewMainMenuScreen", "MainActivity not found!")
                                // Fallback to the provided callback
                                onHistory()
                            }
                        },
                        modifier = Modifier.size(40.dp)
                    ) {
                        CustomIcon(
                            iconRes = R.drawable.ic_calendar,
                            contentDescription = "Calendar",
                            tint = textColor,
                            modifier = Modifier.size(24.dp)
                        )
                    }

                    // Settings icon
                    IconButton(
                        onClick = {
                            Log.d("NewMainMenuScreen", "Settings icon clicked, navigating to settings")
                            // Get the MainActivity directly and use the direct method
                            val mainActivity = context as? MainActivity
                            if (mainActivity != null) {
                                Log.d("NewMainMenuScreen", "Using direct navigation method to settings")
                                mainActivity.navigateToSettings()
                            } else {
                                Log.e("NewMainMenuScreen", "MainActivity not found!")
                                // Fallback to the provided callback
                                onSettings()
                            }
                        },
                        modifier = Modifier.size(40.dp)
                    ) {
                        CustomIcon(
                            iconRes = R.drawable.ic_settings,
                            contentDescription = "Settings",
                            tint = textColor,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(32.dp))

            // Get user name from SharedPreferences
            val prefs = context.getSharedPreferences("user_prefs", Context.MODE_PRIVATE)
            val userName = prefs.getString("user_name", "User") ?: "User"

            // Welcome text - using serif font with user's name
            Text(
                text = "Welkom, $userName",
                style = MaterialTheme.typography.displayMedium.copy(
                    color = accentColor, // Use accent color to match "See you tomorrow" button
                    fontWeight = FontWeight.Bold,
                    fontFamily = FontFamily.Serif
                ),
                textAlign = TextAlign.Center
            )

            // Add weight to push content down to the middle of the screen
            Spacer(modifier = Modifier.weight(1f))

            if (textSavedToday && textSavedInSession) {
                // Reset currentEditingMedia if it's a media type that's already been saved today
                LaunchedEffect(photoSavedToday, videoSavedToday, audioSavedToday) {
                    if ((currentEditingMedia == "photo" && photoSavedToday) ||
                        (currentEditingMedia == "video" && videoSavedToday) ||
                        (currentEditingMedia == "audio" && audioSavedToday)) {
                        currentEditingMedia = null
                    }
                }

                // Reset video playback state when switching between media types
                LaunchedEffect(currentEditingMedia) {
                    if (currentEditingMedia != "video") {
                        // Stop video playback when not in video section
                        shouldPlayVideo = false
                        Log.d("NewMainMenuScreen", "Setting shouldPlayVideo to false (not in video section)")
                    } else if (currentEditingMedia == "video") {
                        // Just force recomposition when entering video section, but don't auto-play
                        videoPlaybackKey++ // Force recomposition
                        shouldPlayVideo = false
                        Log.d("NewMainMenuScreen", "Entering video section (not auto-playing)")
                    }
                }

                when (currentEditingMedia) {
                    "photo" -> {
                        // Photo interface
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp)
                        ) {
                            // No back button needed as we stay on the same screen
                            // Photo preview with buttons at the bottom
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(mediaContentHeight) // Use the same height variable
                            ) {
                                // Photo preview area
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .fillMaxHeight()
                                        .background(backgroundColor)
                                        .border(1.dp, textColor.copy(alpha = 0.5f)),
                                    contentAlignment = Alignment.Center
                                ) {
                                    when {
                                        // Display image from gallery
                                        selectedImageUri != null -> {
                                            val painter = rememberAsyncImagePainter(
                                                ImageRequest.Builder(LocalContext.current)
                                                    .data(selectedImageUri)
                                                    .build()
                                            )
                                            Image(
                                                painter = painter,
                                                contentDescription = "Selected photo from gallery",
                                                modifier = Modifier.fillMaxSize(),
                                                contentScale = ContentScale.Fit
                                            )
                                        }
                                        // Display captured bitmap from camera
                                        capturedBitmap != null -> {
                                            Image(
                                                bitmap = capturedBitmap!!.asImageBitmap(),
                                                contentDescription = "Captured photo",
                                                modifier = Modifier.fillMaxSize(),
                                                contentScale = ContentScale.Fit
                                            )
                                        }
                                        // No photo selected
                                        else -> {
                                            Text("No Photo Selected", style = MaterialTheme.typography.bodyMedium)
                                        }
                                    }
                                }

                                // Error state for camera launch
                                var cameraError by remember { mutableStateOf<String?>(null) }

                                // Show error if there was a problem
                                cameraError?.let {
                                    LaunchedEffect(it) {
                                        Toast.makeText(
                                            context,
                                            "Error launching camera: $it",
                                            Toast.LENGTH_SHORT
                                        ).show()
                                    }
                                }

                                // Conditionally show either selection buttons or action buttons
                                // based on whether a photo has been selected
                                val hasPhoto = selectedImageUri != null || capturedBitmap != null || capturedImageUri != null

                                // Row for buttons - positioned at the bottom of the photo preview, partially inside it
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .align(Alignment.BottomCenter)
                                        .padding(start = 12.dp, end = 12.dp, bottom = 8.dp),
                                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    if (!hasPhoto) {
                                        // Photo selection buttons - only show when no photo is selected
                                        OutlinedButton(
                                            onClick = {
                                                // Open the media gallery (photos and videos)
                                                mediaPickerLauncher.launch("*/*")
                                            },
                                            modifier = Modifier.weight(1f),
                                            contentPadding = PaddingValues(8.dp),
                                            colors = ButtonDefaults.outlinedButtonColors(
                                                containerColor = buttonColor,
                                                contentColor = textColor
                                            ),
                                            shape = RoundedCornerShape(12.dp)
                                        ) {
                                            Text("Choose Photo", color = textColor)
                                        }

                                        OutlinedButton(
                                            onClick = {
                                                Log.d("NewMainMenuScreen", "Launching camera")
                                                // Use the camera launcher without try-catch
                                                cameraError = null
                                                takePictureLauncher.launch(null)
                                            },
                                            modifier = Modifier.weight(1f),
                                            contentPadding = PaddingValues(8.dp),
                                            colors = ButtonDefaults.outlinedButtonColors(
                                                containerColor = buttonColor,
                                                contentColor = textColor
                                            ),
                                            shape = RoundedCornerShape(12.dp)
                                        ) {
                                            Text("Make Photo", color = textColor)
                                        }
                                    } else {
                                        // Action buttons - only show when a photo is selected

                                        // Wipe button
                                        OutlinedButton(
                                            onClick = {
                                                selectedImageUri = null
                                                capturedBitmap = null
                                                capturedImageUri = null
                                            },
                                            modifier = Modifier.weight(1f),
                                            contentPadding = PaddingValues(8.dp),
                                            colors = ButtonDefaults.outlinedButtonColors(
                                                containerColor = buttonColor,
                                                contentColor = textColor
                                            ),
                                            shape = RoundedCornerShape(12.dp)
                                        ) {
                                            Text("Wipe", color = textColor)
                                        }

                                        // Save button
                                        OutlinedButton(
                                            onClick = {
                                                when {
                                                    selectedImageUri != null -> {
                                                        // Save the gallery image URI
                                                        savePhoto("Photo selected from gallery: ${selectedImageUri.toString()}")
                                                        // Record that photo has been saved today
                                                        saveDateManager.recordSave(ContentType.PHOTO)
                                                        showSuccessScreen = true
                                                        currentEditingMedia = null
                                                    }
                                                    capturedImageUri != null -> {
                                                        // Save the captured image URI
                                                        savePhoto("Photo captured with camera: ${capturedImageUri.toString()}")
                                                        // Record that photo has been saved today
                                                        saveDateManager.recordSave(ContentType.PHOTO)
                                                        showSuccessScreen = true
                                                        currentEditingMedia = null
                                                    }
                                                    capturedBitmap != null -> {
                                                        // If we have a bitmap but no URI, try to save it again
                                                        val uri = saveBitmapToFile(context, capturedBitmap!!)
                                                        if (uri != null) {
                                                            capturedImageUri = uri
                                                            savePhoto("Photo captured with camera: ${uri.toString()}")
                                                            // Record that photo has been saved today
                                                            saveDateManager.recordSave(ContentType.PHOTO)
                                                            showSuccessScreen = true
                                                            currentEditingMedia = null
                                                        } else {
                                                            Toast.makeText(context, "Failed to save photo", Toast.LENGTH_SHORT).show()
                                                        }
                                                    }
                                                }
                                            },
                                            modifier = Modifier.weight(1f),
                                            contentPadding = PaddingValues(8.dp),
                                            colors = ButtonDefaults.outlinedButtonColors(
                                                containerColor = buttonColor,
                                                contentColor = textColor
                                            ),
                                            shape = RoundedCornerShape(12.dp)
                                        ) {
                                            Text("Save", color = textColor)
                                        }
                                    }
                                }
                            }

                            // No spacer needed here as the back button is now at the top
                        }
                    }
                    "video" -> {
                        // Video interface
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp)
                        ) {
                            // No back button needed as we stay on the same screen
                            // Reset video playback key when navigating to video section
                            LaunchedEffect(currentEditingMedia) {
                                if (currentEditingMedia == "video") {
                                    Log.d("NewMainMenuScreen", "Resetting video playback state")
                                    videoPlaybackKey++ // Force recomposition
                                    // Don't auto-play video anymore
                                    shouldPlayVideo = false
                                }
                            }

                            // Video preview with buttons at the bottom
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(mediaContentHeight) // Use the same height variable
                            ) {
                                // Video preview area
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .fillMaxHeight()
                                        .background(backgroundColor)
                                        .border(1.dp, textColor.copy(alpha = 0.5f)),
                                    contentAlignment = Alignment.Center
                                ) {
                                    when {
                                        // Display video from gallery
                                        selectedVideoUri != null -> {
                                            // Video view with play/pause button overlay
                                            Box(
                                                modifier = Modifier.fillMaxSize(),
                                                contentAlignment = Alignment.Center
                                            ) {
                                                // Video player with custom layout to prevent black background
                                                AndroidView(
                                                    factory = { ctx ->
                                                        // Create a FrameLayout to hold the VideoView
                                                        val frameLayout = FrameLayout(ctx)
                                                        frameLayout.layoutParams = ViewGroup.LayoutParams(
                                                            ViewGroup.LayoutParams.MATCH_PARENT,
                                                            ViewGroup.LayoutParams.MATCH_PARENT
                                                        )

                                                        // Create the VideoView with a specific size
                                                        val videoView = VideoView(ctx)
                                                        val videoParams = FrameLayout.LayoutParams(
                                                            ViewGroup.LayoutParams.WRAP_CONTENT,
                                                            ViewGroup.LayoutParams.WRAP_CONTENT
                                                        )
                                                        videoParams.gravity = android.view.Gravity.CENTER
                                                        videoView.layoutParams = videoParams

                                                        // Make the VideoView background transparent
                                                        videoView.setBackgroundColor(android.graphics.Color.TRANSPARENT)

                                                        // Set error listener first to catch any errors
                                                        videoView.setOnErrorListener { _, _, _ ->
                                                            Log.e("NewMainMenuScreen", "Error playing video")
                                                            Toast.makeText(context, "Error playing video", Toast.LENGTH_SHORT).show()
                                                            true
                                                        }

                                                        // Set video URI
                                                        videoView.setVideoURI(selectedVideoUri)

                                                        // Set prepared listener
                                                        videoView.setOnPreparedListener { mp ->
                                                            mp.isLooping = true

                                                            // Set the VideoView size based on video dimensions
                                                            val videoWidth = mp.videoWidth
                                                            val videoHeight = mp.videoHeight

                                                            if (videoWidth > 0 && videoHeight > 0) {
                                                                // Calculate the scaling factor to fit within the parent
                                                                val parentWidth = frameLayout.width
                                                                val parentHeight = frameLayout.height

                                                                if (parentWidth > 0 && parentHeight > 0) {
                                                                    val widthScale = parentWidth.toFloat() / videoWidth
                                                                    val heightScale = parentHeight.toFloat() / videoHeight
                                                                    val scale = kotlin.math.min(widthScale, heightScale)

                                                                    val newWidth = (videoWidth * scale).toInt()
                                                                    val newHeight = (videoHeight * scale).toInt()

                                                                    val layoutParams = videoView.layoutParams
                                                                    layoutParams.width = newWidth
                                                                    layoutParams.height = newHeight
                                                                    videoView.layoutParams = layoutParams
                                                                }
                                                            }

                                                            if (shouldPlayVideo) {
                                                                videoView.start()
                                                            } else {
                                                                videoView.pause()
                                                            }
                                                        }

                                                        // Set completion listener to update UI state
                                                        videoView.setOnCompletionListener {
                                                            shouldPlayVideo = false
                                                        }

                                                        // Add the VideoView to the FrameLayout
                                                        frameLayout.addView(videoView)

                                                        // Return the FrameLayout
                                                        frameLayout
                                                    },
                                                    modifier = Modifier.fillMaxSize(),
                                                    // Use update to handle changes to the video URI
                                                    update = { frameLayout ->
                                                        // Find the VideoView inside the FrameLayout
                                                        val videoView = frameLayout.getChildAt(0) as? VideoView
                                                        videoView?.let {
                                                            it.setVideoURI(selectedVideoUri)
                                                            if (shouldPlayVideo) {
                                                                it.start()
                                                            } else {
                                                                it.pause()
                                                            }
                                                        }
                                                    }
                                                )

                                                // Play/Pause button overlay
                                                IconButton(
                                                    onClick = {
                                                        shouldPlayVideo = !shouldPlayVideo
                                                        Log.d("NewMainMenuScreen", "Video play state toggled to: $shouldPlayVideo")
                                                    },
                                                    modifier = Modifier
                                                        .size(56.dp)
                                                        .background(
                                                            color = Color.Black.copy(alpha = 0.5f),
                                                            shape = CircleShape
                                                        )
                                                ) {
                                                    Icon(
                                                        painter = painterResource(
                                                            id = if (shouldPlayVideo) R.drawable.ic_pause else R.drawable.ic_play
                                                        ),
                                                        contentDescription = if (shouldPlayVideo) "Pause" else "Play",
                                                        tint = Color.White,
                                                        modifier = Modifier.size(32.dp)
                                                    )
                                                }
                                            }
                                        }
                                        // Display recorded video
                                        localRecordedVideoPath != null -> {
                                            // Video view with play/pause button overlay
                                            Box(
                                                modifier = Modifier.fillMaxSize(),
                                                contentAlignment = Alignment.Center
                                            ) {
                                                // Video player with custom layout to prevent black background
                                                AndroidView(
                                                    factory = { ctx ->
                                                        // Create a FrameLayout to hold the VideoView
                                                        val frameLayout = FrameLayout(ctx)
                                                        frameLayout.layoutParams = ViewGroup.LayoutParams(
                                                            ViewGroup.LayoutParams.MATCH_PARENT,
                                                            ViewGroup.LayoutParams.MATCH_PARENT
                                                        )

                                                        // Create the VideoView with a specific size
                                                        val videoView = VideoView(ctx)
                                                        val videoParams = FrameLayout.LayoutParams(
                                                            ViewGroup.LayoutParams.WRAP_CONTENT,
                                                            ViewGroup.LayoutParams.WRAP_CONTENT
                                                        )
                                                        videoParams.gravity = android.view.Gravity.CENTER
                                                        videoView.layoutParams = videoParams

                                                        // Make the VideoView background transparent
                                                        videoView.setBackgroundColor(android.graphics.Color.TRANSPARENT)

                                                        // Set error listener first to catch any errors
                                                        videoView.setOnErrorListener { _, _, _ ->
                                                            Log.e("NewMainMenuScreen", "Error playing video")
                                                            Toast.makeText(context, "Error playing video", Toast.LENGTH_SHORT).show()
                                                            true
                                                        }

                                                        // Set video path
                                                        videoView.setVideoPath(localRecordedVideoPath)

                                                        // Set prepared listener
                                                        videoView.setOnPreparedListener { mp ->
                                                            mp.isLooping = true

                                                            // Set the VideoView size based on video dimensions
                                                            val videoWidth = mp.videoWidth
                                                            val videoHeight = mp.videoHeight

                                                            if (videoWidth > 0 && videoHeight > 0) {
                                                                // Calculate the scaling factor to fit within the parent
                                                                val parentWidth = frameLayout.width
                                                                val parentHeight = frameLayout.height

                                                                if (parentWidth > 0 && parentHeight > 0) {
                                                                    val widthScale = parentWidth.toFloat() / videoWidth
                                                                    val heightScale = parentHeight.toFloat() / videoHeight
                                                                    val scale = kotlin.math.min(widthScale, heightScale)

                                                                    val newWidth = (videoWidth * scale).toInt()
                                                                    val newHeight = (videoHeight * scale).toInt()

                                                                    val layoutParams = videoView.layoutParams
                                                                    layoutParams.width = newWidth
                                                                    layoutParams.height = newHeight
                                                                    videoView.layoutParams = layoutParams
                                                                }
                                                            }

                                                            if (shouldPlayVideo) {
                                                                videoView.start()
                                                            } else {
                                                                videoView.pause()
                                                            }
                                                        }

                                                        // Set completion listener to update UI state
                                                        videoView.setOnCompletionListener {
                                                            shouldPlayVideo = false
                                                        }

                                                        // Add the VideoView to the FrameLayout
                                                        frameLayout.addView(videoView)

                                                        // Return the FrameLayout
                                                        frameLayout
                                                    },
                                                    modifier = Modifier.fillMaxSize(),
                                                    // Use update to handle changes to the video path
                                                    update = { frameLayout ->
                                                        // Find the VideoView inside the FrameLayout
                                                        val videoView = frameLayout.getChildAt(0) as? VideoView
                                                        videoView?.let {
                                                            it.setVideoPath(localRecordedVideoPath)
                                                            if (shouldPlayVideo) {
                                                                it.start()
                                                            } else {
                                                                it.pause()
                                                            }
                                                        }
                                                    }
                                                )

                                                // Play/Pause button overlay
                                                IconButton(
                                                    onClick = {
                                                        shouldPlayVideo = !shouldPlayVideo
                                                        Log.d("NewMainMenuScreen", "Video play state toggled to: $shouldPlayVideo")
                                                    },
                                                    modifier = Modifier
                                                        .size(56.dp)
                                                        .background(
                                                            color = Color.Black.copy(alpha = 0.5f),
                                                            shape = CircleShape
                                                        )
                                                ) {
                                                    Icon(
                                                        painter = painterResource(
                                                            id = if (shouldPlayVideo) R.drawable.ic_pause else R.drawable.ic_play
                                                        ),
                                                        contentDescription = if (shouldPlayVideo) "Pause" else "Play",
                                                        tint = Color.White,
                                                        modifier = Modifier.size(32.dp)
                                                    )
                                                }
                                            }
                                        }
                                        // No video selected
                                        else -> {
                                            Text("No Video Selected", style = MaterialTheme.typography.bodyMedium)
                                        }
                                    }
                                }

                                // Conditionally show either selection buttons or action buttons
                                // based on whether a video has been selected
                                val hasVideo = selectedVideoUri != null || localRecordedVideoPath != null

                                // Row for buttons - positioned at the bottom of the video preview, partially inside it
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .align(Alignment.BottomCenter)
                                        .padding(start = 12.dp, end = 12.dp, bottom = 8.dp),
                                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    if (!hasVideo) {
                                        // Video selection buttons - only show when no video is selected
                                        OutlinedButton(
                                            onClick = {
                                                // Open the media gallery (photos and videos)
                                                mediaPickerLauncher.launch("*/*")
                                            },
                                            modifier = Modifier.weight(1f),
                                            contentPadding = PaddingValues(8.dp),
                                            colors = ButtonDefaults.outlinedButtonColors(
                                                containerColor = buttonColor,
                                                contentColor = textColor
                                            ),
                                            shape = RoundedCornerShape(12.dp)
                                        ) {
                                            Text("Choose Video", color = textColor)
                                        }

                                        OutlinedButton(
                                            onClick = {
                                                // Start video recording
                                                val videoFile = createVideoFileForMainMenu(context)
                                                if (videoFile != null) {
                                                    try {
                                                        // Get URI for the file using FileProvider
                                                        val videoUri = FileProvider.getUriForFile(
                                                            context,
                                                            "${context.packageName}.provider",
                                                            videoFile
                                                        )

                                                        // Launch video recording intent with proper configuration
                                                        val takeVideoIntent = Intent(MediaStore.ACTION_VIDEO_CAPTURE).apply {
                                                            putExtra(MediaStore.EXTRA_OUTPUT, videoUri)
                                                            putExtra(MediaStore.EXTRA_DURATION_LIMIT, 15) // 15 seconds max
                                                            putExtra(MediaStore.EXTRA_VIDEO_QUALITY, 1) // High quality

                                                            // Grant permissions to the receiving app
                                                            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                                                            addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION)
                                                        }

                                                        // Check if there's an app that can handle this intent
                                                        if (takeVideoIntent.resolveActivity(context.packageManager) != null) {
                                                            // Start the activity
                                                            (context as? Activity)?.startActivityForResult(
                                                                takeVideoIntent,
                                                                1001 // Request code for video capture
                                                            )

                                                            // Store the path for later use
                                                            // Set shouldPlayVideo to false - video won't play automatically
                                                            shouldPlayVideo = false
                                                            // Clear any previous selection
                                                            selectedVideoUri = null
                                                            // Set the new path in both local state and MainActivity
                                                            localRecordedVideoPath = videoFile.absolutePath
                                                            // Store the path in MainActivity for access after recording
                                                            (context as? MainActivity)?.recordedVideoPath = videoFile.absolutePath
                                                            // Force recomposition
                                                            videoPlaybackKey++
                                                            Log.d("NewMainMenuScreen", "Video recording started to: ${videoFile.absolutePath}")
                                                            // Don't auto-play video - user will use the play button
                                                            Log.d("NewMainMenuScreen", "Video recording will not auto-play")
                                                        } else {
                                                            Toast.makeText(
                                                                context,
                                                                "No app available to handle video recording",
                                                                Toast.LENGTH_SHORT
                                                            ).show()
                                                        }
                                                    } catch (e: Exception) {
                                                        Log.e("NewMainMenuScreen", "Error starting video capture activity", e)
                                                        Toast.makeText(
                                                            context,
                                                            "Error starting camera: ${e.message}",
                                                            Toast.LENGTH_SHORT
                                                        ).show()
                                                    }
                                                } else {
                                                    Toast.makeText(context, "Failed to create video file", Toast.LENGTH_SHORT).show()
                                                }
                                            },
                                            modifier = Modifier.weight(1f),
                                            contentPadding = PaddingValues(8.dp),
                                            colors = ButtonDefaults.outlinedButtonColors(
                                                containerColor = buttonColor,
                                                contentColor = textColor
                                            ),
                                            shape = RoundedCornerShape(12.dp)
                                        ) {
                                            Text("Record Video", color = textColor)
                                        }
                                    } else {
                                        // Action buttons - only show when a video is selected

                                        // Wipe button
                                        OutlinedButton(
                                            onClick = {
                                                // Stop playback and clear sources
                                                shouldPlayVideo = false
                                                selectedVideoUri = null
                                                localRecordedVideoPath = null
                                                videoPlaybackKey++ // Force recomposition
                                            },
                                            modifier = Modifier.weight(1f),
                                            contentPadding = PaddingValues(8.dp),
                                            colors = ButtonDefaults.outlinedButtonColors(
                                                containerColor = buttonColor,
                                                contentColor = textColor
                                            ),
                                            shape = RoundedCornerShape(12.dp)
                                        ) {
                                            Text("Wipe", color = textColor)
                                        }

                                        // Save button
                                        OutlinedButton(
                                            onClick = {
                                                when {
                                                    selectedVideoUri != null -> {
                                                        // Save the gallery video URI
                                                        saveVideo("Video selected from gallery: ${selectedVideoUri.toString()}")
                                                        // Record that video has been saved today
                                                        saveDateManager.recordSave(ContentType.VIDEO)
                                                        showSuccessScreen = true
                                                        currentEditingMedia = null
                                                    }
                                                    localRecordedVideoPath != null -> {
                                                        // Save the recorded video path
                                                        saveVideo("Video recorded: $localRecordedVideoPath")
                                                        // Record that video has been saved today
                                                        saveDateManager.recordSave(ContentType.VIDEO)
                                                        showSuccessScreen = true
                                                        currentEditingMedia = null
                                                    }
                                                }
                                            },
                                            modifier = Modifier.weight(1f),
                                            contentPadding = PaddingValues(8.dp),
                                            colors = ButtonDefaults.outlinedButtonColors(
                                                containerColor = buttonColor,
                                                contentColor = textColor
                                            ),
                                            shape = RoundedCornerShape(12.dp)
                                        ) {
                                            Text("Save", color = textColor)
                                        }
                                    }
                                }
                            }

                            // No spacer needed here as the back button is now at the top
                        }
                    }
                    "audio" -> {
                        // Audio interface
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp)
                        ) {
                            // No back button needed as we stay on the same screen
                            // Audio player/recorder UI with buttons at the bottom
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(mediaContentHeight) // Use the same height variable
                            ) {
                                // Audio preview area
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .fillMaxHeight()
                                        .background(backgroundColor)
                                        .border(1.dp, textColor.copy(alpha = 0.5f)),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Column(
                                        horizontalAlignment = Alignment.CenterHorizontally,
                                        verticalArrangement = Arrangement.Center
                                    ) {
                                        when {
                                            // Display audio from device
                                            selectedAudioUri != null -> {
                                                Text(
                                                    text = "Audio Selected",
                                                    style = MaterialTheme.typography.bodyMedium,
                                                    modifier = Modifier.padding(bottom = 8.dp)
                                                )

                                                // Play/Pause button
                                                IconButton(
                                                    onClick = {
                                                        if (isPlaying) {
                                                            mediaPlayer?.pause()
                                                            isPlaying = false
                                                        } else {
                                                            if (mediaPlayer == null) {
                                                                // Use a local variable to avoid smart cast issue with delegated property
                                                                val uri = selectedAudioUri
                                                                if (uri != null) {
                                                                        Log.d("NewMainMenuScreen", "Playing audio from URI: $uri")

                                                                        val player = MediaPlayer()
                                                                        player.setOnErrorListener { _, what, extra ->
                                                                            Log.e("NewMainMenuScreen", "MediaPlayer error: what=$what, extra=$extra")
                                                                            Toast.makeText(
                                                                                context,
                                                                                "Error playing audio (code: $what)",
                                                                                Toast.LENGTH_SHORT
                                                                            ).show()
                                                                            isPlaying = false
                                                                            true // Error handled
                                                                        }

                                                                        try {
                                                                            player.setDataSource(context, uri)
                                                                            player.setOnPreparedListener { mp ->
                                                                                Log.d("NewMainMenuScreen", "Audio prepared successfully")
                                                                                mp.start()
                                                                            }
                                                                            player.setOnCompletionListener {
                                                                                Log.d("NewMainMenuScreen", "Audio playback completed")
                                                                                isPlaying = false
                                                                            }
                                                                            player.prepareAsync()
                                                                            mediaPlayer = player
                                                                            isPlaying = true
                                                                        } catch (e: Exception) {
                                                                            Log.e("NewMainMenuScreen", "Error playing audio", e)
                                                                            Toast.makeText(context, "Error playing audio", Toast.LENGTH_SHORT).show()
                                                                            player.release()
                                                                        }
                                                                }
                                                            } else {
                                                                val player = mediaPlayer
                                                                if (player != null) {
                                                                    try {
                                                                        player.start()
                                                                        isPlaying = true
                                                                    } catch (e: Exception) {
                                                                        Log.e("NewMainMenuScreen", "Error resuming audio", e)
                                                                        Toast.makeText(context, "Error resuming audio", Toast.LENGTH_SHORT).show()
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    },
                                                    modifier = Modifier
                                                        .size(48.dp)
                                                        .border(1.dp, textColor, CircleShape)
                                                ) {
                                                    Icon(
                                                        painter = painterResource(
                                                            id = if (isPlaying) R.drawable.ic_pause else R.drawable.ic_play
                                                        ),
                                                        contentDescription = if (isPlaying) "Pause" else "Play",
                                                        tint = textColor
                                                    )
                                                }
                                            }
                                            // Display recorded audio
                                            localRecordedAudioPath != null -> {
                                                Text(
                                                    text = "Audio Recorded",
                                                    style = MaterialTheme.typography.bodyMedium,
                                                    modifier = Modifier.padding(bottom = 8.dp)
                                                )

                                                // Play/Pause button
                                                IconButton(
                                                    onClick = {
                                                        if (isPlaying) {
                                                            mediaPlayer?.pause()
                                                            isPlaying = false
                                                        } else {
                                                            if (mediaPlayer == null) {
                                                                // Check if the file exists and has content
                                                                val file = File(localRecordedAudioPath ?: "")
                                                                if (!file.exists() || file.length() == 0L) {
                                                                    val errorMsg = if (!file.exists()) {
                                                                        Log.e("NewMainMenuScreen", "Audio file does not exist: $localRecordedAudioPath")
                                                                        "Audio file not found"
                                                                    } else {
                                                                        Log.e("NewMainMenuScreen", "Audio file is empty: $localRecordedAudioPath")
                                                                        "Audio file is empty"
                                                                    }
                                                                    Toast.makeText(
                                                                        context,
                                                                        errorMsg,
                                                                        Toast.LENGTH_SHORT
                                                                    ).show()
                                                                } else {
                                                                        Log.d("NewMainMenuScreen", "Playing audio from file: $localRecordedAudioPath (size: ${file.length()} bytes)")

                                                                        val player = MediaPlayer()
                                                                        player.setOnErrorListener { _, what, extra ->
                                                                            Log.e("NewMainMenuScreen", "MediaPlayer error: what=$what, extra=$extra")
                                                                            Toast.makeText(
                                                                                context,
                                                                                "Error playing audio (code: $what)",
                                                                                Toast.LENGTH_SHORT
                                                                            ).show()
                                                                            isPlaying = false
                                                                            true // Error handled
                                                                        }

                                                                        try {
                                                                            player.setDataSource(localRecordedAudioPath)
                                                                            player.setOnPreparedListener { mp ->
                                                                                Log.d("NewMainMenuScreen", "Audio prepared successfully")
                                                                                mp.start()
                                                                            }
                                                                            player.setOnCompletionListener {
                                                                                Log.d("NewMainMenuScreen", "Audio playback completed")
                                                                                isPlaying = false
                                                                            }
                                                                            player.prepareAsync()
                                                                            mediaPlayer = player
                                                                            isPlaying = true
                                                                        } catch (e: Exception) {
                                                                            Log.e("NewMainMenuScreen", "Error playing audio", e)
                                                                            Toast.makeText(context, "Error playing audio", Toast.LENGTH_SHORT).show()
                                                                            player.release()
                                                                        }
                                                                }
                                                            } else {
                                                                val player = mediaPlayer
                                                                if (player != null) {
                                                                    try {
                                                                        player.start()
                                                                        isPlaying = true
                                                                    } catch (e: Exception) {
                                                                        Log.e("NewMainMenuScreen", "Error resuming audio", e)
                                                                        Toast.makeText(context, "Error resuming audio", Toast.LENGTH_SHORT).show()
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    },
                                                    modifier = Modifier
                                                        .size(48.dp)
                                                        .border(1.dp, textColor, CircleShape)
                                                ) {
                                                    Icon(
                                                        painter = painterResource(
                                                            id = if (isPlaying) R.drawable.ic_pause else R.drawable.ic_play
                                                        ),
                                                        contentDescription = if (isPlaying) "Pause" else "Play",
                                                        tint = textColor
                                                    )
                                                }
                                            }
                                            // No audio selected
                                            else -> {
                                                Text("No Audio Selected", style = MaterialTheme.typography.bodyMedium)
                                            }
                                        }
                                    }
                                }

                                // Conditionally show either selection buttons or action buttons
                                // based on whether audio has been selected
                                val hasAudio = selectedAudioUri != null || localRecordedAudioPath != null

                                // Row for buttons - positioned at the bottom of the audio preview, partially inside it
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .align(Alignment.BottomCenter)
                                        .padding(start = 12.dp, end = 12.dp, bottom = 8.dp),
                                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    if (!hasAudio) {
                                        // Audio selection buttons - only show when no audio is selected
                                        OutlinedButton(
                                            onClick = {
                                                // Open the audio library
                                                audioPickerLauncher.launch("audio/*")
                                            },
                                            modifier = Modifier.weight(1f),
                                            contentPadding = PaddingValues(8.dp),
                                            colors = ButtonDefaults.outlinedButtonColors(
                                                containerColor = buttonColor,
                                                contentColor = textColor
                                            ),
                                            shape = RoundedCornerShape(12.dp)
                                        ) {
                                            Text("Choose Audio", color = textColor)
                                        }

                                        OutlinedButton(
                                            onClick = {
                                                Log.d("NewMainMenuScreen", "Navigating to audio recorder screen from Record Audio button")
                                                // Navigate to the dedicated audio recorder screen
                                                (context as? MainActivity)?.let { mainActivity ->
                                                    // Use the direct navigation method
                                                    mainActivity.navigateToAudioRecorder()
                                                }
                                            },
                                            modifier = Modifier.weight(1f),
                                            contentPadding = PaddingValues(8.dp),
                                            colors = ButtonDefaults.outlinedButtonColors(
                                                containerColor = buttonColor,
                                                contentColor = textColor
                                            ),
                                            shape = RoundedCornerShape(12.dp)
                                        ) {
                                            Text("Record Audio", color = textColor)
                                        }
                                    } else {
                                        // Action buttons - only show when audio is selected

                                        // Wipe button
                                        OutlinedButton(
                                            onClick = {
                                                selectedAudioUri = null
                                                localRecordedAudioPath = null
                                                // Release media player if it exists
                                                mediaPlayer?.apply {
                                                    if (isPlaying) stop()
                                                    release()
                                                }
                                                mediaPlayer = null
                                                isPlaying = false
                                            },
                                            modifier = Modifier.weight(1f),
                                            contentPadding = PaddingValues(8.dp),
                                            colors = ButtonDefaults.outlinedButtonColors(
                                                containerColor = buttonColor,
                                                contentColor = textColor
                                            ),
                                            shape = RoundedCornerShape(12.dp)
                                        ) {
                                            Text("Wipe", color = textColor)
                                        }

                                        // Save button
                                        OutlinedButton(
                                            onClick = {
                                                when {
                                                    selectedAudioUri != null -> {
                                                        // Save the gallery audio URI
                                                        saveAudio("Audio selected from device: ${selectedAudioUri.toString()}")
                                                        // Record that audio has been saved today
                                                        saveDateManager.recordSave(ContentType.AUDIO)
                                                        showSuccessScreen = true
                                                        currentEditingMedia = null
                                                    }
                                                    localRecordedAudioPath != null -> {
                                                        // Save the recorded audio path
                                                        saveAudio("Audio recorded: $localRecordedAudioPath")
                                                        // Record that audio has been saved today
                                                        saveDateManager.recordSave(ContentType.AUDIO)
                                                        showSuccessScreen = true
                                                        currentEditingMedia = null
                                                    }
                                                }
                                            },
                                            modifier = Modifier.weight(1f),
                                            contentPadding = PaddingValues(8.dp),
                                            colors = ButtonDefaults.outlinedButtonColors(
                                                containerColor = buttonColor,
                                                contentColor = textColor
                                            ),
                                            shape = RoundedCornerShape(12.dp)
                                        ) {
                                            Text("Save", color = textColor)
                                        }
                                    }
                                }
                            }

                            // No spacer needed here as the back button is now at the top
                        }
                    }
                    else -> {
                        // Keep the same size box as the text field but show a message
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(mediaContentHeight),
                            contentAlignment = Alignment.Center
                        ) {
                            // Background with same styling as text field
                            Box(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .border(
                                        width = 1.dp,
                                        color = textColor.copy(alpha = 0.5f),
                                        shape = RoundedCornerShape(4.dp)
                                    )
                                    .background(
                                        color = buttonColor.copy(alpha = 0.2f),
                                        shape = RoundedCornerShape(4.dp)
                                    )
                            )

                            // Message text
                            Text(
                                text = "Add a video, photo and/or audio file.",
                                style = MaterialTheme.typography.bodyLarge.copy(
                                    color = textColor,
                                    fontWeight = FontWeight.Medium
                                ),
                                modifier = Modifier.padding(16.dp),
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                }
            } else {
                // Text input field with buttons at the bottom
                // Define color variables for consistent button styling
                val orangeAccent = getSoftPastelColor() // Soft pastel color for disabled states
                val darkerBackground = getSoftComplementaryBackgroundColor() // Soft complementary background for disabled buttons

                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(mediaContentHeight)
                ) {
                    // Text field takes the full space
                    OutlinedTextField(
                        value = text,
                        onValueChange = { if (it.length <= 150) text = it },
                        label = { Text("Jouw zin van vandaag", color = textColor) },
                        singleLine = false,
                        maxLines = 5, // Increased from 3 to 5 for more space
                        modifier = Modifier
                            .fillMaxWidth()
                            .fillMaxHeight(),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = accentColor,
                            unfocusedBorderColor = textColor.copy(alpha = 0.5f),
                            focusedLabelColor = accentColor,
                            unfocusedLabelColor = textColor.copy(alpha = 0.7f),
                            cursorColor = textColor,
                            focusedTextColor = textColor,
                            unfocusedTextColor = textColor
                        ),
                        textStyle = MaterialTheme.typography.bodyLarge.copy(
                            fontWeight = FontWeight.Normal,
                            lineHeight = 24.sp
                        )
                    )

                    // Row for buttons - positioned at the bottom of the text field, partially inside it
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .align(Alignment.BottomCenter)
                            .padding(start = 12.dp, end = 12.dp, bottom = 8.dp),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        // Wipe button
                        OutlinedButton(
                            onClick = { text = "" },
                            modifier = Modifier
                                .weight(1f),
                            contentPadding = PaddingValues(8.dp),
                            colors = standardOutlinedButtonColors(),
                            shape = RoundedCornerShape(12.dp),
                            enabled = text.isNotEmpty()
                        ) {
                            Text("Wipe")
                        }

                        // Save button
                        OutlinedButton(
                            onClick = {
                                if (text.isNotEmpty()) {
                                    onSaveText(text)
                                    showSuccessScreen = true
                                    // Set text saved in session to true
                                    textSavedInSession = true
                                    // Clear the text after saving
                                    text = ""
                                }
                            },
                            modifier = Modifier
                                .weight(1f),
                            contentPadding = PaddingValues(8.dp),
                            colors = standardOutlinedButtonColors(),
                            shape = RoundedCornerShape(12.dp),
                            enabled = text.isNotEmpty()
                        ) {
                            Text("Opslaan")
                        }
                    }
                }

                if (text.length == 150) {
                    Text(
                        text = "maximum allowed is 150 characters",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.error
                    )
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Media buttons row - positioned under the text field
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Title removed as requested

                if (!textSavedToday) {
                    Text(
                        text = "Save text first",
                        style = MaterialTheme.typography.bodySmall.copy(
                            color = textColor.copy(alpha = 0.6f),
                            fontStyle = FontStyle.Italic
                        )
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                // Text icon - always show but non-clickable (users start with text input)

                OutlinedButton(
                    onClick = { /* Not clickable - text input is the default */ },
                    modifier = Modifier
                        .size(75.dp)
                        .border(
                            width = 1.5.dp,
                            color = orangeAccent, // Orange border
                            shape = RoundedCornerShape(12.dp)
                        ),
                    contentPadding = PaddingValues(8.dp),
                    colors = ButtonDefaults.outlinedButtonColors(
                        containerColor = buttonColor, // Not used since we override with disabled colors
                        contentColor = textColor, // Not used since we override with disabled colors
                        disabledContainerColor = darkerBackground, // Darker background when disabled
                        disabledContentColor = orangeAccent // Orange content when disabled
                    ),
                    shape = RoundedCornerShape(12.dp), // Square shape with rounded corners like other buttons
                    enabled = false // Always disabled since text input is the default
                ) {
                    CustomIcon(
                        iconRes = R.drawable.ic_text,
                        contentDescription = "Text",
                        tint = orangeAccent, // Orange icon
                        modifier = Modifier.size(28.dp)
                    )
                }

                // Photo button - always show, but disabled if already saved today
                OutlinedButton(
                    onClick = {
                        currentEditingMedia = "photo"
                    },
                    modifier = Modifier
                        .size(75.dp)
                        .border(
                            width = 1.5.dp,
                            color = if (currentEditingMedia == "photo") {
                                // Orange border when selected (matches disabled style)
                                orangeAccent
                            } else if (photoSavedToday) {
                                // Soft pastel border when disabled (saved)
                                orangeAccent
                            } else {
                                // Soft complementary border when enabled
                                getSoftComplementaryBackgroundColor()
                            },
                            shape = RoundedCornerShape(12.dp)
                        ),
                    contentPadding = PaddingValues(8.dp),
                    colors = ButtonDefaults.outlinedButtonColors(
                        containerColor = if (currentEditingMedia == "photo") darkerBackground else buttonColor,
                        contentColor = if (currentEditingMedia == "photo") orangeAccent else textColor,
                        disabledContainerColor = if (photoSavedToday) {
                            // Darker background when disabled (saved)
                            darkerBackground
                        } else {
                            buttonColor.copy(alpha = 0.5f)
                        },
                        disabledContentColor = if (photoSavedToday) {
                            // Orange content when disabled (saved)
                            orangeAccent
                        } else {
                            textColor.copy(alpha = 0.3f)
                        }
                    ),
                    elevation = if (textSavedToday && !photoSavedToday) standardButtonElevation() else null, // Drop shadow for enabled buttons
                    shape = RoundedCornerShape(12.dp),
                    enabled = textSavedToday && !photoSavedToday
                ) {
                    CustomIcon(
                        iconRes = R.drawable.ic_photo,
                        contentDescription = "Photo",
                        tint = if (currentEditingMedia == "photo") {
                            // Orange icon when selected (matches disabled style)
                            orangeAccent
                        } else if (photoSavedToday) {
                            // Soft pastel icon when disabled (saved)
                            orangeAccent
                        } else if (textSavedToday) {
                            // Soft complementary color when enabled (matches border)
                            getSoftComplementaryBackgroundColor()
                        } else {
                            textColor.copy(alpha = 0.3f)
                        },
                        modifier = Modifier.size(28.dp)
                    )
                }

                // Video button - always show, but disabled if already saved today
                OutlinedButton(
                    onClick = {
                        currentEditingMedia = "video"
                    },
                    modifier = Modifier
                        .size(75.dp)
                        .border(
                            width = 1.5.dp,
                            color = if (currentEditingMedia == "video") {
                                // Orange border when selected (matches disabled style)
                                orangeAccent
                            } else if (videoSavedToday) {
                                // Soft pastel border when disabled (saved)
                                orangeAccent
                            } else {
                                // Soft complementary border when enabled
                                getSoftComplementaryBackgroundColor()
                            },
                            shape = RoundedCornerShape(12.dp)
                        ),
                    contentPadding = PaddingValues(8.dp),
                    colors = ButtonDefaults.outlinedButtonColors(
                        containerColor = if (currentEditingMedia == "video") darkerBackground else buttonColor,
                        contentColor = if (currentEditingMedia == "video") orangeAccent else textColor,
                        disabledContainerColor = if (videoSavedToday) {
                            // Darker background when disabled (saved)
                            darkerBackground
                        } else {
                            buttonColor.copy(alpha = 0.5f)
                        },
                        disabledContentColor = if (videoSavedToday) {
                            // Orange content when disabled (saved)
                            orangeAccent
                        } else {
                            textColor.copy(alpha = 0.3f)
                        }
                    ),
                    elevation = if (textSavedToday && !videoSavedToday) standardButtonElevation() else null, // Drop shadow for enabled buttons
                    shape = RoundedCornerShape(12.dp),
                    enabled = textSavedToday && !videoSavedToday
                ) {
                    CustomIcon(
                        iconRes = R.drawable.ic_video,
                        contentDescription = "Video",
                        tint = if (currentEditingMedia == "video") {
                            // Orange icon when selected (matches disabled style)
                            orangeAccent
                        } else if (videoSavedToday) {
                            // Soft pastel icon when disabled (saved)
                            orangeAccent
                        } else if (textSavedToday) {
                            // Soft complementary color when enabled (matches border)
                            getSoftComplementaryBackgroundColor()
                        } else {
                            textColor.copy(alpha = 0.3f)
                        },
                        modifier = Modifier.size(28.dp)
                    )
                }

                // Audio button - always show, but disabled if already saved today
                OutlinedButton(
                    onClick = {
                        // Just set the current editing media to audio to show the audio options
                        // This will display the audio editing section with options to record or choose audio
                        currentEditingMedia = "audio"
                        Log.d("NewMainMenuScreen", "Showing audio options")
                    },
                    modifier = Modifier
                        .size(75.dp)
                        .border(
                            width = 1.5.dp,
                            color = if (currentEditingMedia == "audio") {
                                // Orange border when selected (matches disabled style)
                                orangeAccent
                            } else if (audioSavedToday) {
                                // Soft pastel border when disabled (saved)
                                orangeAccent
                            } else {
                                // Soft complementary border when enabled
                                getSoftComplementaryBackgroundColor()
                            },
                            shape = RoundedCornerShape(12.dp)
                        ),
                    contentPadding = PaddingValues(8.dp),
                    colors = ButtonDefaults.outlinedButtonColors(
                        containerColor = if (currentEditingMedia == "audio") darkerBackground else buttonColor,
                        contentColor = if (currentEditingMedia == "audio") orangeAccent else textColor,
                        disabledContainerColor = if (audioSavedToday) {
                            // Darker background when disabled (saved)
                            darkerBackground
                        } else {
                            buttonColor.copy(alpha = 0.5f)
                        },
                        disabledContentColor = if (audioSavedToday) {
                            // Orange content when disabled (saved)
                            orangeAccent
                        } else {
                            textColor.copy(alpha = 0.3f)
                        }
                    ),
                    elevation = if (textSavedToday && !audioSavedToday) standardButtonElevation() else null, // Drop shadow for enabled buttons
                    shape = RoundedCornerShape(12.dp),
                    enabled = textSavedToday && !audioSavedToday
                ) {
                    CustomIcon(
                        iconRes = R.drawable.ic_audio,
                        contentDescription = "Audio",
                        tint = if (currentEditingMedia == "audio") {
                            // Orange icon when selected (matches disabled style)
                            orangeAccent
                        } else if (audioSavedToday) {
                            // Soft pastel icon when disabled (saved)
                            orangeAccent
                        } else if (textSavedToday) {
                            // Soft complementary color when enabled (matches border)
                            getSoftComplementaryBackgroundColor()
                        } else {
                            textColor.copy(alpha = 0.3f)
                        },
                        modifier = Modifier.size(28.dp)
                    )
                }

                // No need for a message anymore since we're showing disabled buttons
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Mood section
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Title removed as requested

                if (moodSavedToday) {
                    Text(
                        text = "Your mood for today: ${
                            when (selectedMood) {
                                "very_sad" -> "Very Sad"
                                "sad" -> "Sad"
                                "neutral" -> "Neutral"
                                "happy" -> "Happy"
                                "very_happy" -> "Very Happy"
                                else -> "Selected"
                            }
                        }",
                        style = MaterialTheme.typography.bodySmall.copy(
                            color = textColor.copy(alpha = 0.6f),
                            fontStyle = FontStyle.Italic
                        )
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Show mood buttons - if mood is saved, only show the selected one
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = if (moodSavedToday) Arrangement.Center else Arrangement.SpaceEvenly
            ) {
                // Very sad face - only show if not saved or if this is the selected mood
                if (!moodSavedToday || selectedMood == "very_sad") {
                    OutlinedButton(
                    onClick = {
                        selectedMood = "very_sad"
                        // Save the mood
                        saveMood("Mood: Very Sad")
                        // Show success message
                        showSuccessScreen = true
                    },
                    modifier = Modifier
                        .size(50.dp)
                        .border(
                            width = 1.5.dp,
                            color = if (selectedMood == "very_sad") {
                                // Same border color as photo button when selected
                                orangeAccent
                            } else if (moodSavedToday) {
                                // Soft pastel border when disabled (saved)
                                orangeAccent
                            } else {
                                // Soft complementary border when enabled
                                getSoftComplementaryBackgroundColor()
                            },
                            shape = CircleShape
                        ),
                    contentPadding = PaddingValues(0.dp),
                    colors = ButtonDefaults.outlinedButtonColors(
                        containerColor = if (selectedMood == "very_sad") accentColor.copy(alpha = 0.3f) else buttonColor,
                        contentColor = textColor,
                        disabledContainerColor = if (moodSavedToday) {
                            // Darker background when disabled (saved)
                            darkerBackground
                        } else {
                            buttonColor.copy(alpha = 0.5f)
                        },
                        disabledContentColor = if (moodSavedToday) {
                            // Orange content when disabled (saved)
                            orangeAccent
                        } else {
                            textColor.copy(alpha = 0.3f)
                        }
                    ),
                    elevation = if (textSavedToday && !moodSavedToday) standardButtonElevation() else null, // Drop shadow for enabled buttons
                    shape = CircleShape,
                    enabled = textSavedToday && !moodSavedToday
                ) {
                    CustomIcon(
                        iconRes = R.drawable.ic_mood_very_sad,
                        contentDescription = "Very Sad",
                        tint = if (moodSavedToday) {
                            // Soft pastel icon when disabled (saved)
                            orangeAccent
                        } else if (textSavedToday) {
                            // Soft complementary color when enabled (matches border)
                            getSoftComplementaryBackgroundColor()
                        } else {
                            textColor.copy(alpha = 0.3f)
                        },
                        modifier = Modifier.size(24.dp)
                    )
                    }
                }

                // Sad face - only show if not saved or if this is the selected mood
                if (!moodSavedToday || selectedMood == "sad") {
                    OutlinedButton(
                    onClick = {
                        selectedMood = "sad"
                        // Save the mood
                        saveMood("Mood: Sad")
                        // Show success message
                        showSuccessScreen = true
                    },
                    modifier = Modifier
                        .size(50.dp)
                        .border(
                            width = 1.5.dp,
                            color = if (selectedMood == "sad") {
                                // Same border color as photo button when selected
                                orangeAccent
                            } else if (moodSavedToday) {
                                // Soft pastel border when disabled (saved)
                                orangeAccent
                            } else {
                                // Soft complementary border when enabled
                                getSoftComplementaryBackgroundColor()
                            },
                            shape = CircleShape
                        ),
                    contentPadding = PaddingValues(0.dp),
                    colors = ButtonDefaults.outlinedButtonColors(
                        containerColor = if (selectedMood == "sad") accentColor.copy(alpha = 0.3f) else buttonColor,
                        contentColor = textColor,
                        disabledContainerColor = if (moodSavedToday) {
                            // Darker background when disabled (saved)
                            darkerBackground
                        } else {
                            buttonColor.copy(alpha = 0.5f)
                        },
                        disabledContentColor = if (moodSavedToday) {
                            // Orange content when disabled (saved)
                            orangeAccent
                        } else {
                            textColor.copy(alpha = 0.3f)
                        }
                    ),
                    elevation = if (textSavedToday && !moodSavedToday) standardButtonElevation() else null, // Drop shadow for enabled buttons
                    shape = CircleShape,
                    enabled = textSavedToday && !moodSavedToday
                ) {
                    CustomIcon(
                        iconRes = R.drawable.ic_mood_sad,
                        contentDescription = "Sad",
                        tint = if (moodSavedToday) {
                            // Soft pastel icon when disabled (saved)
                            orangeAccent
                        } else if (textSavedToday) {
                            // Soft complementary color when enabled (matches border)
                            getSoftComplementaryBackgroundColor()
                        } else {
                            textColor.copy(alpha = 0.3f)
                        },
                        modifier = Modifier.size(24.dp)
                    )
                    }
                }

                // Neutral face - only show if not saved or if this is the selected mood
                if (!moodSavedToday || selectedMood == "neutral") {
                    OutlinedButton(
                    onClick = {
                        selectedMood = "neutral"
                        // Save the mood
                        saveMood("Mood: Neutral")
                        // Show success message
                        showSuccessScreen = true
                    },
                    modifier = Modifier
                        .size(50.dp)
                        .border(
                            width = 1.5.dp,
                            color = if (selectedMood == "neutral") {
                                // Same border color as photo button when selected
                                orangeAccent
                            } else if (moodSavedToday) {
                                // Soft pastel border when disabled (saved)
                                orangeAccent
                            } else {
                                // Soft complementary border when enabled
                                getSoftComplementaryBackgroundColor()
                            },
                            shape = CircleShape
                        ),
                    contentPadding = PaddingValues(0.dp),
                    colors = ButtonDefaults.outlinedButtonColors(
                        containerColor = if (selectedMood == "neutral") accentColor.copy(alpha = 0.3f) else buttonColor,
                        contentColor = textColor,
                        disabledContainerColor = if (moodSavedToday) {
                            // Darker background when disabled (saved)
                            darkerBackground
                        } else {
                            buttonColor.copy(alpha = 0.5f)
                        },
                        disabledContentColor = if (moodSavedToday) {
                            // Orange content when disabled (saved)
                            orangeAccent
                        } else {
                            textColor.copy(alpha = 0.3f)
                        }
                    ),
                    elevation = if (textSavedToday && !moodSavedToday) standardButtonElevation() else null, // Drop shadow for enabled buttons
                    shape = CircleShape,
                    enabled = textSavedToday && !moodSavedToday
                ) {
                    CustomIcon(
                        iconRes = R.drawable.ic_mood_neutral,
                        contentDescription = "Neutral",
                        tint = if (moodSavedToday) {
                            // Soft pastel icon when disabled (saved)
                            orangeAccent
                        } else if (textSavedToday) {
                            // Soft complementary color when enabled (matches border)
                            getSoftComplementaryBackgroundColor()
                        } else {
                            textColor.copy(alpha = 0.3f)
                        },
                        modifier = Modifier.size(24.dp)
                    )
                    }
                }

                // Happy face - only show if not saved or if this is the selected mood
                if (!moodSavedToday || selectedMood == "happy") {
                    OutlinedButton(
                    onClick = {
                        selectedMood = "happy"
                        // Save the mood
                        saveMood("Mood: Happy")
                        // Show success message
                        showSuccessScreen = true
                    },
                    modifier = Modifier
                        .size(50.dp)
                        .border(
                            width = 1.5.dp,
                            color = if (selectedMood == "happy") {
                                // Same border color as photo button when selected
                                orangeAccent
                            } else if (moodSavedToday) {
                                // Soft pastel border when disabled (saved)
                                orangeAccent
                            } else {
                                // Soft complementary border when enabled
                                getSoftComplementaryBackgroundColor()
                            },
                            shape = CircleShape
                        ),
                    contentPadding = PaddingValues(0.dp),
                    colors = ButtonDefaults.outlinedButtonColors(
                        containerColor = if (selectedMood == "happy") accentColor.copy(alpha = 0.3f) else buttonColor,
                        contentColor = textColor,
                        disabledContainerColor = if (moodSavedToday) {
                            // Darker background when disabled (saved)
                            darkerBackground
                        } else {
                            buttonColor.copy(alpha = 0.5f)
                        },
                        disabledContentColor = if (moodSavedToday) {
                            // Orange content when disabled (saved)
                            orangeAccent
                        } else {
                            textColor.copy(alpha = 0.3f)
                        }
                    ),
                    elevation = if (textSavedToday && !moodSavedToday) standardButtonElevation() else null, // Drop shadow for enabled buttons
                    shape = CircleShape,
                    enabled = textSavedToday && !moodSavedToday
                ) {
                    CustomIcon(
                        iconRes = R.drawable.ic_mood_happy,
                        contentDescription = "Happy",
                        tint = if (moodSavedToday) {
                            // Soft pastel icon when disabled (saved)
                            orangeAccent
                        } else if (textSavedToday) {
                            // Soft complementary color when enabled (matches border)
                            getSoftComplementaryBackgroundColor()
                        } else {
                            textColor.copy(alpha = 0.3f)
                        },
                        modifier = Modifier.size(24.dp)
                    )
                    }
                }

                // Very happy face - only show if not saved or if this is the selected mood
                if (!moodSavedToday || selectedMood == "very_happy") {
                    OutlinedButton(
                    onClick = {
                        selectedMood = "very_happy"
                        // Save the mood
                        saveMood("Mood: Very Happy")
                        // Show success message
                        showSuccessScreen = true
                    },
                    modifier = Modifier
                        .size(50.dp)
                        .border(
                            width = 1.5.dp,
                            color = if (selectedMood == "very_happy") {
                                // Same border color as photo button when selected
                                orangeAccent
                            } else if (moodSavedToday) {
                                // Soft pastel border when disabled (saved)
                                orangeAccent
                            } else {
                                // Soft complementary border when enabled
                                getSoftComplementaryBackgroundColor()
                            },
                            shape = CircleShape
                        ),
                    contentPadding = PaddingValues(0.dp),
                    colors = ButtonDefaults.outlinedButtonColors(
                        containerColor = if (selectedMood == "very_happy") accentColor.copy(alpha = 0.3f) else buttonColor,
                        contentColor = textColor,
                        disabledContainerColor = if (moodSavedToday) {
                            // Darker background when disabled (saved)
                            darkerBackground
                        } else {
                            buttonColor.copy(alpha = 0.5f)
                        },
                        disabledContentColor = if (moodSavedToday) {
                            // Orange content when disabled (saved)
                            orangeAccent
                        } else {
                            textColor.copy(alpha = 0.3f)
                        }
                    ),
                    elevation = if (textSavedToday && !moodSavedToday) standardButtonElevation() else null, // Drop shadow for enabled buttons
                    shape = CircleShape,
                    enabled = textSavedToday && !moodSavedToday
                ) {
                    CustomIcon(
                        iconRes = R.drawable.ic_mood_very_happy,
                        contentDescription = "Very Happy",
                        tint = if (moodSavedToday) {
                            // Soft pastel icon when disabled (saved)
                            orangeAccent
                        } else if (textSavedToday) {
                            // Soft complementary color when enabled (matches border)
                            getSoftComplementaryBackgroundColor()
                        } else {
                            textColor.copy(alpha = 0.3f)
                        },
                        modifier = Modifier.size(24.dp)
                    )
                    }
                }
            }

            Spacer(modifier = Modifier.height(32.dp))

            // No bottom navigation bar

            Spacer(modifier = Modifier.weight(0.5f))

            // No back button - removed as requested

            // "See you tomorrow!" button that closes the app
            Button(
                onClick = {
                    // Check if there's unsaved text before closing the app
                    if (text.isNotEmpty() && !textSavedToday) {
                        showSaveDialog = true
                    } else {
                        // Close the app
                        activity?.finishAndRemoveTask()
                    }
                },
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = accentColor
                ),
                shape = RoundedCornerShape(12.dp),
                elevation = ButtonDefaults.buttonElevation(defaultElevation = 4.dp)
            ) {
                Text(
                    "See you tomorrow!",
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Medium
                    )
                )
            }

            Spacer(modifier = Modifier.height(16.dp))
        }

        // Success popup dialog
        if (showSuccessScreen) {
            SuccessPopupDialog(
                onDismiss = { showSuccessScreen = false }
            )

            // Automatically hide success popup after a delay
            LaunchedEffect(showSuccessScreen) {
                delay(1500)
                showSuccessScreen = false
            }
        }
    }
}

@Composable
fun SuccessPopupDialog(
    onDismiss: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Card(
            modifier = Modifier
                .wrapContentSize()
                .padding(5.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
            shape = RoundedCornerShape(8.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            border = BorderStroke(3.dp, MaterialTheme.colorScheme.outline)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center,
                modifier = Modifier.padding(5.dp)
            ) {
                Text(
                    text = "✓",
                    style = MaterialTheme.typography.headlineMedium,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.padding(end = 6.dp)
                )
                Text(
                    text = "Saved!",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
        }
    }
}